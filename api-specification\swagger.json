{"openapi": "3.0.3", "info": {"title": "SRSR Property Management API", "description": "Comprehensive API for SRSR Property Management System with RBAC, real-time updates, and multi-property management capabilities", "version": "1.0.0", "contact": {"name": "SRSR Property Management", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "https://api.srsrproperty.com/v1", "description": "Production server"}, {"url": "https://staging-api.srsrproperty.com/v1", "description": "Staging server"}, {"url": "http://localhost:3000/v1", "description": "Development server"}], "security": [{"bearerAuth": []}], "paths": {"/auth/login": {"post": {"tags": ["Authentication"], "summary": "User login", "description": "Authenticate user and return JWT token with user permissions", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "429": {"description": "Too many login attempts", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/auth/refresh": {"post": {"tags": ["Authentication"], "summary": "Refresh access token", "description": "Refresh JWT token using refresh token", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"refreshToken": {"type": "string", "description": "Valid refresh token"}}, "required": ["refreshToken"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthToken"}}}}, "401": {"description": "Invalid refresh token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/auth/logout": {"post": {"tags": ["Authentication"], "summary": "User logout", "description": "Invalidate user session and tokens", "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Logout successful"}}}}}}}}}, "/users/profile": {"get": {"tags": ["Users"], "summary": "Get current user profile", "description": "Retrieve current authenticated user's profile information", "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Users"], "summary": "Update user profile", "description": "Update current user's profile information", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "Profile updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/users": {"get": {"tags": ["Users"], "summary": "List users", "description": "Get paginated list of users (Admin only)", "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"name": "role", "in": "query", "description": "Filter by user role", "schema": {"$ref": "#/components/schemas/UserRole"}}, {"name": "propertyId", "in": "query", "description": "Filter by assigned property", "schema": {"type": "string", "format": "uuid"}}, {"name": "search", "in": "query", "description": "Search by name or email", "schema": {"type": "string"}}], "responses": {"200": {"description": "Users retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedUsersResponse"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Users"], "summary": "Create new user", "description": "Create a new user account (Admin only)", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}, "responses": {"201": {"description": "User created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "User already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/users/{userId}": {"get": {"tags": ["Users"], "summary": "Get user by ID", "description": "Retrieve specific user information", "parameters": [{"$ref": "#/components/parameters/UserIdParam"}], "responses": {"200": {"description": "User retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Users"], "summary": "Update user", "description": "Update user information (Admin only)", "parameters": [{"$ref": "#/components/parameters/UserIdParam"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "User updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Users"], "summary": "Delete user", "description": "Soft delete user account (Admin only)", "parameters": [{"$ref": "#/components/parameters/UserIdParam"}], "responses": {"204": {"description": "User deleted successfully"}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for authentication"}}, "parameters": {"PageParam": {"name": "page", "in": "query", "description": "Page number for pagination", "schema": {"type": "integer", "minimum": 1, "default": 1}}, "LimitParam": {"name": "limit", "in": "query", "description": "Number of items per page", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, "UserIdParam": {"name": "userId", "in": "path", "required": true, "description": "User ID", "schema": {"type": "string", "format": "uuid"}}, "PropertyIdParam": {"name": "propertyId", "in": "path", "required": true, "description": "Property ID", "schema": {"type": "string", "format": "uuid"}}}, "schemas": {"LoginRequest": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "description": "User email address"}, "password": {"type": "string", "minLength": 6, "description": "User password"}, "deviceId": {"type": "string", "description": "Device identifier for tracking"}, "deviceName": {"type": "string", "description": "Human-readable device name"}}}, "LoginResponse": {"type": "object", "required": ["user", "token", "message"], "properties": {"user": {"$ref": "#/components/schemas/User"}, "token": {"$ref": "#/components/schemas/AuthToken"}, "message": {"type": "string", "example": "Login successful"}}}, "AuthToken": {"type": "object", "required": ["accessToken", "refreshToken", "expiresAt", "tokenType"], "properties": {"accessToken": {"type": "string", "description": "JWT access token"}, "refreshToken": {"type": "string", "description": "JWT refresh token"}, "expiresAt": {"type": "string", "format": "date-time", "description": "Token expiration timestamp"}, "tokenType": {"type": "string", "enum": ["Bearer"], "default": "Bearer"}}}, "User": {"type": "object", "required": ["id", "name", "email", "phone", "role", "assignedProperties", "isActive", "createdAt", "lastLogin", "permissions"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique user identifier"}, "name": {"type": "string", "description": "Full name of the user"}, "email": {"type": "string", "format": "email", "description": "Email address"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$", "description": "Phone number in E.164 format"}, "role": {"$ref": "#/components/schemas/UserRole"}, "assignedProperties": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "List of property IDs assigned to user"}, "isActive": {"type": "boolean", "description": "Whether the user account is active"}, "createdAt": {"type": "string", "format": "date-time", "description": "Account creation timestamp"}, "lastLogin": {"type": "string", "format": "date-time", "description": "Last login timestamp"}, "permissions": {"$ref": "#/components/schemas/UserPermissions"}, "avatar": {"type": "string", "format": "uri", "description": "URL to user's avatar image"}, "timezone": {"type": "string", "description": "User's timezone (IANA format)"}, "language": {"type": "string", "enum": ["en", "hi", "te"], "default": "en", "description": "Preferred language"}}}, "UserRole": {"type": "string", "enum": ["super_admin", "property_manager", "office_manager", "security_personnel", "maintenance_staff", "construction_supervisor"], "description": "User role determining access permissions"}, "UserPermissions": {"type": "object", "required": ["canViewDashboard", "canManageProperties", "canManageOffice", "canManageSecurity", "canManageMaintenance", "canManageUsers", "canViewReports", "canExportData", "allowedScreens", "allowedActions"], "properties": {"canViewDashboard": {"type": "boolean"}, "canManageProperties": {"type": "boolean"}, "canManageOffice": {"type": "boolean"}, "canManageSecurity": {"type": "boolean"}, "canManageMaintenance": {"type": "boolean"}, "canManageUsers": {"type": "boolean"}, "canViewReports": {"type": "boolean"}, "canExportData": {"type": "boolean"}, "allowedScreens": {"type": "array", "items": {"type": "string"}, "description": "List of screens user can access"}, "allowedActions": {"type": "array", "items": {"type": "string", "enum": ["create", "read", "update", "delete", "export", "import"]}, "description": "List of actions user can perform"}}}}}}