{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/lib/*": ["./src/lib/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/middleware/*": ["./src/middleware/*"], "@/routes/*": ["./src/routes/*"], "@/services/*": ["./src/services/*"], "@/models/*": ["./src/models/*"]}, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./dist"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist"]}