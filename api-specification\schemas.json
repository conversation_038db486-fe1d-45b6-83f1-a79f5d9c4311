{"schemas": {"Property": {"type": "object", "required": ["id", "name", "type", "address", "description", "isActive", "createdAt", "updatedAt"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique property identifier"}, "name": {"type": "string", "description": "Property name"}, "type": {"$ref": "#/components/schemas/PropertyType"}, "address": {"type": "string", "description": "Property address"}, "description": {"type": "string", "description": "Property description"}, "isActive": {"type": "boolean", "description": "Whether property is active"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "coordinates": {"$ref": "#/components/schemas/Coordinates"}, "images": {"type": "array", "items": {"type": "string", "format": "uri"}, "description": "Property images"}}}, "PropertyDetail": {"allOf": [{"$ref": "#/components/schemas/Property"}, {"type": "object", "properties": {"systemStatuses": {"type": "array", "items": {"$ref": "#/components/schemas/SystemStatusSummary"}}, "settings": {"$ref": "#/components/schemas/PropertySettings"}, "recentActivities": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}, "statistics": {"$ref": "#/components/schemas/PropertyStatistics"}}}]}, "PropertyType": {"type": "string", "enum": ["residential", "office", "construction"], "description": "Type of property"}, "SystemType": {"type": "string", "enum": ["water", "electricity", "security", "internet", "ott", "maintenance"], "description": "Type of system"}, "SystemStatusSummary": {"type": "object", "required": ["id", "propertyId", "systemType", "status", "lastUpdated"], "properties": {"id": {"type": "string", "format": "uuid"}, "propertyId": {"type": "string", "format": "uuid"}, "systemType": {"$ref": "#/components/schemas/SystemType"}, "status": {"$ref": "#/components/schemas/SystemStatus"}, "description": {"type": "string"}, "lastUpdated": {"type": "string", "format": "date-time"}, "healthScore": {"type": "number", "minimum": 0, "maximum": 100, "description": "System health score (0-100)"}}}, "SystemStatus": {"type": "string", "enum": ["operational", "warning", "critical", "offline"], "description": "System operational status"}, "SystemDetail": {"type": "object", "required": ["id", "propertyId", "systemType", "status", "lastUpdated"], "properties": {"id": {"type": "string", "format": "uuid"}, "propertyId": {"type": "string", "format": "uuid"}, "systemType": {"$ref": "#/components/schemas/SystemType"}, "status": {"$ref": "#/components/schemas/SystemStatus"}, "description": {"type": "string"}, "lastUpdated": {"type": "string", "format": "date-time"}, "metadata": {"type": "object", "additionalProperties": true, "description": "System-specific metadata"}, "configuration": {"type": "object", "additionalProperties": true, "description": "System configuration"}, "metrics": {"type": "array", "items": {"$ref": "#/components/schemas/SystemMetric"}}, "alerts": {"type": "array", "items": {"$ref": "#/components/schemas/Alert"}}, "contacts": {"type": "array", "items": {"$ref": "#/components/schemas/ContactInfo"}}}}, "SystemMetric": {"type": "object", "required": ["name", "value", "unit", "timestamp"], "properties": {"name": {"type": "string", "description": "Metric name"}, "value": {"type": "number", "description": "Metric value"}, "unit": {"type": "string", "description": "Unit of measurement"}, "timestamp": {"type": "string", "format": "date-time"}, "threshold": {"type": "object", "properties": {"min": {"type": "number"}, "max": {"type": "number"}, "warning": {"type": "number"}, "critical": {"type": "number"}}}}}, "ContactInfo": {"type": "object", "required": ["id", "name", "phone", "role", "category"], "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "email": {"type": "string", "format": "email"}, "role": {"type": "string"}, "category": {"type": "string", "enum": ["vendor", "service_provider", "emergency", "internal"]}, "isActive": {"type": "boolean", "default": true}, "availability": {"type": "object", "properties": {"hours": {"type": "string", "description": "Available hours (e.g., '9 AM - 6 PM')"}, "days": {"type": "array", "items": {"type": "string", "enum": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]}}}}}}, "DashboardOverview": {"type": "object", "required": ["systemHealth", "properties", "alerts", "statistics"], "properties": {"systemHealth": {"$ref": "#/components/schemas/SystemHealthSummary"}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/PropertySummary"}}, "alerts": {"type": "array", "items": {"$ref": "#/components/schemas/Alert"}}, "statistics": {"$ref": "#/components/schemas/DashboardStatistics"}, "recentActivities": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}}}, "SystemHealthSummary": {"type": "object", "required": ["overallHealth", "totalSystems", "operationalCount", "warningCount", "criticalCount"], "properties": {"overallHealth": {"type": "number", "minimum": 0, "maximum": 100, "description": "Overall system health percentage"}, "totalSystems": {"type": "integer", "minimum": 0}, "operationalCount": {"type": "integer", "minimum": 0}, "warningCount": {"type": "integer", "minimum": 0}, "criticalCount": {"type": "integer", "minimum": 0}, "offlineCount": {"type": "integer", "minimum": 0}, "systemBreakdown": {"type": "array", "items": {"$ref": "#/components/schemas/SystemTypeHealth"}}}}, "SystemTypeHealth": {"type": "object", "required": ["systemType", "status", "count"], "properties": {"systemType": {"$ref": "#/components/schemas/SystemType"}, "status": {"$ref": "#/components/schemas/SystemStatus"}, "count": {"type": "integer", "minimum": 0}, "healthScore": {"type": "number", "minimum": 0, "maximum": 100}}}, "PropertySummary": {"type": "object", "required": ["id", "name", "type", "status", "systemCount"], "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "type": {"$ref": "#/components/schemas/PropertyType"}, "status": {"$ref": "#/components/schemas/SystemStatus"}, "systemCount": {"type": "integer", "minimum": 0}, "lastUpdated": {"type": "string", "format": "date-time"}, "healthScore": {"type": "number", "minimum": 0, "maximum": 100}}}, "Alert": {"type": "object", "required": ["id", "title", "description", "severity", "status", "createdAt"], "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "description": {"type": "string"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "status": {"type": "string", "enum": ["open", "acknowledged", "resolved"]}, "propertyId": {"type": "string", "format": "uuid"}, "systemType": {"$ref": "#/components/schemas/SystemType"}, "createdAt": {"type": "string", "format": "date-time"}, "acknowledgedAt": {"type": "string", "format": "date-time"}, "resolvedAt": {"type": "string", "format": "date-time"}, "assignedTo": {"type": "string", "format": "uuid", "description": "User ID assigned to handle the alert"}, "metadata": {"type": "object", "additionalProperties": true}}}}}