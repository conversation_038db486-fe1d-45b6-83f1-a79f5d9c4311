{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:go_router/go_router.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:srsr_property_management/presentation/screens/auth/login_screen.dart", "transitive": false}, {"uri": "package:srsr_property_management/presentation/screens/main/main_navigation_screen.dart", "transitive": false}, {"uri": "package:srsr_property_management/presentation/screens/dashboard/dashboard_screen.dart", "transitive": false}, {"uri": "package:srsr_property_management/presentation/screens/properties/properties_screen.dart", "transitive": false}, {"uri": "package:srsr_property_management/presentation/screens/properties/property_detail_screen.dart", "transitive": false}, {"uri": "package:srsr_property_management/presentation/screens/office/office_management_screen.dart", "transitive": false}, {"uri": "package:srsr_property_management/presentation/screens/settings/settings_screen.dart", "transitive": false}, {"uri": "package:srsr_property_management/presentation/screens/splash/splash_screen.dart", "transitive": false}], "elements": []}