{"Property": {"type": "object", "required": ["id", "name", "type", "address", "description", "isActive", "createdAt", "updatedAt"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique property identifier"}, "name": {"type": "string", "description": "Property name", "example": "Jublee Hills Home"}, "type": {"$ref": "#/components/schemas/PropertyType"}, "address": {"type": "string", "description": "Property address", "example": "Road No. 36, Jubilee Hills, Hyderabad"}, "description": {"type": "string", "description": "Property description"}, "isActive": {"type": "boolean", "description": "Whether property is active"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "coordinates": {"$ref": "#/components/schemas/Coordinates"}, "images": {"type": "array", "items": {"type": "string", "format": "uri"}, "description": "Property images"}}}, "PropertyType": {"type": "string", "enum": ["residential", "office", "construction"], "description": "Type of property"}, "SystemStatus": {"type": "string", "enum": ["operational", "warning", "critical", "offline"], "description": "System operational status"}, "SystemType": {"type": "string", "enum": ["water", "electricity", "security", "internet", "ott", "maintenance"], "description": "Type of system"}, "PropertyDetail": {"allOf": [{"$ref": "#/components/schemas/Property"}, {"type": "object", "properties": {"systemStatuses": {"type": "array", "items": {"$ref": "#/components/schemas/SystemStatusSummary"}}, "settings": {"$ref": "#/components/schemas/PropertySettings"}, "recentActivities": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}, "statistics": {"$ref": "#/components/schemas/PropertyStatistics"}}}]}, "SystemStatusSummary": {"type": "object", "required": ["id", "propertyId", "systemType", "status", "lastUpdated"], "properties": {"id": {"type": "string", "format": "uuid"}, "propertyId": {"type": "string", "format": "uuid"}, "systemType": {"$ref": "#/components/schemas/SystemType"}, "status": {"$ref": "#/components/schemas/SystemStatus"}, "description": {"type": "string"}, "lastUpdated": {"type": "string", "format": "date-time"}, "healthScore": {"type": "number", "minimum": 0, "maximum": 100, "description": "System health score (0-100)"}, "metadata": {"type": "object", "additionalProperties": true, "description": "System-specific metadata"}}}, "DashboardOverview": {"type": "object", "required": ["systemHealth", "properties", "alerts", "statistics"], "properties": {"systemHealth": {"$ref": "#/components/schemas/SystemHealthSummary"}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/PropertySummary"}}, "alerts": {"type": "array", "items": {"$ref": "#/components/schemas/Alert"}}, "statistics": {"$ref": "#/components/schemas/DashboardStatistics"}, "recentActivities": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}}}, "SystemHealthSummary": {"type": "object", "required": ["overallHealth", "totalSystems", "operationalCount", "warningCount", "criticalCount"], "properties": {"overallHealth": {"type": "number", "minimum": 0, "maximum": 100, "description": "Overall system health percentage", "example": 85.5}, "totalSystems": {"type": "integer", "minimum": 0, "example": 35}, "operationalCount": {"type": "integer", "minimum": 0, "example": 28}, "warningCount": {"type": "integer", "minimum": 0, "example": 4}, "criticalCount": {"type": "integer", "minimum": 0, "example": 3}, "offlineCount": {"type": "integer", "minimum": 0, "example": 0}, "systemBreakdown": {"type": "array", "items": {"$ref": "#/components/schemas/SystemTypeHealth"}}}}, "SystemTypeHealth": {"type": "object", "required": ["systemType", "status", "count"], "properties": {"systemType": {"$ref": "#/components/schemas/SystemType"}, "status": {"$ref": "#/components/schemas/SystemStatus"}, "count": {"type": "integer", "minimum": 0}, "healthScore": {"type": "number", "minimum": 0, "maximum": 100}}}, "PropertySummary": {"type": "object", "required": ["id", "name", "type", "status", "systemCount"], "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "example": "Jublee Hills Home"}, "type": {"$ref": "#/components/schemas/PropertyType"}, "status": {"$ref": "#/components/schemas/SystemStatus"}, "systemCount": {"type": "integer", "minimum": 0, "example": 6}, "lastUpdated": {"type": "string", "format": "date-time"}, "healthScore": {"type": "number", "minimum": 0, "maximum": 100, "example": 92.5}}}, "Alert": {"type": "object", "required": ["id", "title", "description", "severity", "status", "createdAt"], "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "example": "Generator Fuel Low"}, "description": {"type": "string", "example": "Jublee Hills Home - 20% fuel remaining"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "status": {"type": "string", "enum": ["open", "acknowledged", "resolved"]}, "propertyId": {"type": "string", "format": "uuid"}, "systemType": {"$ref": "#/components/schemas/SystemType"}, "createdAt": {"type": "string", "format": "date-time"}, "acknowledgedAt": {"type": "string", "format": "date-time"}, "resolvedAt": {"type": "string", "format": "date-time"}, "assignedTo": {"type": "string", "format": "uuid", "description": "User ID assigned to handle the alert"}, "metadata": {"type": "object", "additionalProperties": true}}}, "Activity": {"type": "object", "required": ["id", "title", "description", "type", "timestamp"], "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "example": "Generator fuel updated"}, "description": {"type": "string", "example": "Fuel level updated to 60%"}, "type": {"type": "string", "enum": ["system_update", "maintenance", "security", "user_action", "alert"]}, "propertyId": {"type": "string", "format": "uuid"}, "systemType": {"$ref": "#/components/schemas/SystemType"}, "userId": {"type": "string", "format": "uuid"}, "timestamp": {"type": "string", "format": "date-time"}, "metadata": {"type": "object", "additionalProperties": true}}}, "Office": {"type": "object", "required": ["id", "name", "address", "type", "isActive"], "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "example": "Back Office - <PERSON><PERSON>"}, "address": {"type": "string", "example": "Hyderabad"}, "type": {"type": "string", "enum": ["office", "construction_site"]}, "isActive": {"type": "boolean"}, "employees": {"type": "array", "items": {"$ref": "#/components/schemas/Employee"}}, "settings": {"$ref": "#/components/schemas/OfficeSettings"}, "statistics": {"$ref": "#/components/schemas/OfficeStatistics"}}}, "Employee": {"type": "object", "required": ["id", "name", "mobile", "role", "dutyStartTime", "dutyEndTime", "isActive"], "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "mobile": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$", "example": "9849011797"}, "role": {"type": "string", "enum": ["full_time", "contract", "support_staff", "helper", "gardener", "security_guard", "supervisor"]}, "dutyStartTime": {"type": "string", "pattern": "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$", "example": "09:15"}, "dutyEndTime": {"type": "string", "pattern": "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$", "example": "18:45"}, "team": {"type": "string", "example": "<PERSON><PERSON><PERSON>'s Team"}, "remarks": {"type": "string"}, "isActive": {"type": "boolean"}, "joinedDate": {"type": "string", "format": "date"}}}, "AttendanceResponse": {"type": "object", "required": ["date", "officeId", "records"], "properties": {"date": {"type": "string", "format": "date"}, "officeId": {"type": "string", "format": "uuid"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/AttendanceRecord"}}, "summary": {"$ref": "#/components/schemas/AttendanceSummary"}}}, "AttendanceRecord": {"type": "object", "required": ["employeeId", "status", "hoursWorked"], "properties": {"employeeId": {"type": "string", "format": "uuid"}, "employeeName": {"type": "string"}, "status": {"type": "string", "enum": ["present", "absent", "late", "half_day"]}, "checkInTime": {"type": "string", "pattern": "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"}, "checkOutTime": {"type": "string", "pattern": "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"}, "hoursWorked": {"type": "integer", "minimum": 0, "maximum": 24}, "remarks": {"type": "string"}}}, "SubmitAttendanceRequest": {"type": "object", "required": ["date", "records"], "properties": {"date": {"type": "string", "format": "date"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/AttendanceRecord"}}}}, "AttendanceSubmissionResponse": {"type": "object", "required": ["message", "recordsProcessed"], "properties": {"message": {"type": "string", "example": "Attendance submitted successfully"}, "recordsProcessed": {"type": "integer", "minimum": 0}, "errors": {"type": "array", "items": {"type": "object", "properties": {"employeeId": {"type": "string", "format": "uuid"}, "error": {"type": "string"}}}}}}, "ErrorResponse": {"type": "object", "required": ["error", "message", "timestamp"], "properties": {"error": {"type": "string", "description": "Error code"}, "message": {"type": "string", "description": "Human-readable error message"}, "timestamp": {"type": "string", "format": "date-time"}, "path": {"type": "string", "description": "API endpoint that caused the error"}, "details": {"type": "object", "additionalProperties": true, "description": "Additional error details"}}}, "PaginatedPropertiesResponse": {"type": "object", "required": ["data", "pagination"], "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Property"}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}}, "PaginationInfo": {"type": "object", "required": ["page", "limit", "total", "totalPages"], "properties": {"page": {"type": "integer", "minimum": 1}, "limit": {"type": "integer", "minimum": 1}, "total": {"type": "integer", "minimum": 0}, "totalPages": {"type": "integer", "minimum": 0}, "hasNext": {"type": "boolean"}, "hasPrev": {"type": "boolean"}}}}