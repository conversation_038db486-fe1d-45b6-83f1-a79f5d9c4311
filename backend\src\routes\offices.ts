import { Router } from 'express';
import { UserRole } from '@prisma/client';
import { prisma } from '@/lib/prisma';
import { 
  createOfficeSchema, 
  createEmployeeSchema, 
  submitAttendanceSchema,
  attendanceQuerySchema 
} from '@/lib/validation';
import { validateRequest, asyncHandler, AppError } from '@/middleware/errorHandler';
import { authenticate, authorize, requireAction } from '@/middleware/auth';
import { conditionalRateLimit } from '@/middleware/rateLimiter';
import { 
  OfficeWithStats,
  AttendanceResponse,
  AttendanceRecordWithEmployee,
  AttendanceSummary,
  SubmitAttendanceRequest,
  AttendanceSubmissionResponse 
} from '@/types';

const router = Router();

// Apply authentication and rate limiting
router.use(authenticate);
router.use(conditionalRateLimit);

/**
 * @swagger
 * /offices:
 *   get:
 *     tags: [Office Management]
 *     summary: List offices
 *     description: Get list of office locations and construction sites
 *     parameters:
 *       - in: query
 *         name: type
 *         description: Filter by office type
 *         schema:
 *           type: string
 *           enum: [OFFICE, CONSTRUCTION_SITE]
 *     responses:
 *       200:
 *         description: Offices retrieved successfully
 */
router.get('/',
  authorize([], 'canManageOffice'),
  asyncHandler(async (req, res) => {
    const { type } = req.query;
    const user = req.user!;

    let whereClause: any = { isActive: true };
    if (type) {
      whereClause.type = type;
    }

    const offices = await prisma.office.findMany({
      where: whereClause,
      include: {
        _count: {
          select: {
            employees: {
              where: { isActive: true },
            },
            attendanceRecords: {
              where: {
                date: {
                  gte: new Date(new Date().setHours(0, 0, 0, 0)),
                  lt: new Date(new Date().setHours(23, 59, 59, 999)),
                },
                status: 'PRESENT',
              },
            },
          },
        },
      },
      orderBy: { name: 'asc' },
    });

    // Calculate attendance rates
    const officesWithStats: OfficeWithStats[] = await Promise.all(
      offices.map(async (office) => {
        const employeeCount = office._count.employees;
        const presentToday = office._count.attendanceRecords;
        
        // Calculate attendance rate for the last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        const totalAttendanceRecords = await prisma.attendanceRecord.count({
          where: {
            officeId: office.id,
            date: { gte: thirtyDaysAgo },
          },
        });
        
        const presentRecords = await prisma.attendanceRecord.count({
          where: {
            officeId: office.id,
            date: { gte: thirtyDaysAgo },
            status: 'PRESENT',
          },
        });
        
        const attendanceRate = totalAttendanceRecords > 0 
          ? Math.round((presentRecords / totalAttendanceRecords) * 100)
          : 0;

        return {
          ...office,
          employeeCount,
          presentToday,
          attendanceRate,
        };
      })
    );

    res.json({
      success: true,
      data: officesWithStats,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /offices:
 *   post:
 *     tags: [Office Management]
 *     summary: Create new office
 *     description: Create a new office or construction site
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateOfficeRequest'
 *     responses:
 *       201:
 *         description: Office created successfully
 */
router.post('/',
  authorize([UserRole.SUPER_ADMIN, UserRole.OFFICE_MANAGER], 'canManageOffice'),
  requireAction('create'),
  validateRequest(createOfficeSchema),
  asyncHandler(async (req, res) => {
    const officeData = req.body;
    const user = req.user!;

    const office = await prisma.office.create({
      data: officeData,
    });

    // Log activity
    await prisma.activity.create({
      data: {
        userId: user.id,
        action: 'CREATE_OFFICE',
        description: `Created office: ${office.name}`,
        metadata: { officeType: office.type },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
      },
    });

    res.status(201).json({
      success: true,
      data: office,
      message: 'Office created successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /offices/{officeId}/employees:
 *   get:
 *     tags: [Office Management]
 *     summary: Get office employees
 *     description: Retrieve list of employees for an office
 *     parameters:
 *       - in: path
 *         name: officeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Employees retrieved successfully
 */
router.get('/:officeId/employees',
  authorize([], 'canManageOffice'),
  asyncHandler(async (req, res) => {
    const { officeId } = req.params;

    const employees = await prisma.employee.findMany({
      where: { 
        officeId,
        isActive: true,
      },
      orderBy: { name: 'asc' },
    });

    res.json({
      success: true,
      data: employees,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /offices/{officeId}/employees:
 *   post:
 *     tags: [Office Management]
 *     summary: Add employee to office
 *     description: Add a new employee to an office
 *     parameters:
 *       - in: path
 *         name: officeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateEmployeeRequest'
 *     responses:
 *       201:
 *         description: Employee added successfully
 */
router.post('/:officeId/employees',
  authorize([UserRole.SUPER_ADMIN, UserRole.OFFICE_MANAGER], 'canManageOffice'),
  requireAction('create'),
  validateRequest(createEmployeeSchema),
  asyncHandler(async (req, res) => {
    const { officeId } = req.params;
    const employeeData = req.body;
    const user = req.user!;

    // Check if office exists
    const office = await prisma.office.findUnique({
      where: { id: officeId },
    });

    if (!office) {
      throw new AppError('Office not found', 404, 'OFFICE_NOT_FOUND');
    }

    // Check if employee ID is unique
    const existingEmployee = await prisma.employee.findUnique({
      where: { employeeId: employeeData.employeeId },
    });

    if (existingEmployee) {
      throw new AppError('Employee ID already exists', 400, 'DUPLICATE_EMPLOYEE_ID');
    }

    const employee = await prisma.employee.create({
      data: {
        ...employeeData,
        officeId,
        joinDate: new Date(employeeData.joinDate),
      },
    });

    // Log activity
    await prisma.activity.create({
      data: {
        userId: user.id,
        action: 'CREATE_EMPLOYEE',
        description: `Added employee: ${employee.name} to ${office.name}`,
        metadata: { 
          employeeId: employee.employeeId,
          officeName: office.name,
        },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
      },
    });

    res.status(201).json({
      success: true,
      data: employee,
      message: 'Employee added successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /offices/{officeId}/attendance:
 *   get:
 *     tags: [Office Management]
 *     summary: Get attendance records
 *     description: Retrieve attendance records for an office
 *     parameters:
 *       - in: path
 *         name: officeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: date
 *         description: Specific date (YYYY-MM-DD)
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: startDate
 *         description: Start date for range query
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         description: End date for range query
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Attendance records retrieved successfully
 */
router.get('/:officeId/attendance',
  authorize([], 'canManageOffice'),
  validateRequest(attendanceQuerySchema, 'query'),
  asyncHandler(async (req, res) => {
    const { officeId } = req.params;
    const { date, startDate, endDate } = req.query as any;

    // Check if office exists
    const office = await prisma.office.findUnique({
      where: { id: officeId },
    });

    if (!office) {
      throw new AppError('Office not found', 404, 'OFFICE_NOT_FOUND');
    }

    // Build date filter
    let dateFilter: any = {};
    if (date) {
      const targetDate = new Date(date);
      dateFilter = {
        gte: new Date(targetDate.setHours(0, 0, 0, 0)),
        lt: new Date(targetDate.setHours(23, 59, 59, 999)),
      };
    } else if (startDate && endDate) {
      dateFilter = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else {
      // Default to today
      const today = new Date();
      dateFilter = {
        gte: new Date(today.setHours(0, 0, 0, 0)),
        lt: new Date(today.setHours(23, 59, 59, 999)),
      };
    }

    const attendanceRecords = await prisma.attendanceRecord.findMany({
      where: {
        officeId,
        date: dateFilter,
      },
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            employeeId: true,
            designation: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { date: 'desc' },
    });

    // Calculate summary
    const total = attendanceRecords.length;
    const present = attendanceRecords.filter(r => r.status === 'PRESENT').length;
    const absent = attendanceRecords.filter(r => r.status === 'ABSENT').length;
    const late = attendanceRecords.filter(r => r.status === 'LATE').length;
    const halfDay = attendanceRecords.filter(r => r.status === 'HALF_DAY').length;
    const leave = attendanceRecords.filter(r => r.status === 'LEAVE').length;
    const attendanceRate = total > 0 ? Math.round((present / total) * 100) : 0;

    const summary: AttendanceSummary = {
      total,
      present,
      absent,
      late,
      halfDay,
      leave,
      attendanceRate,
    };

    const response: AttendanceResponse = {
      date: date || new Date().toISOString().split('T')[0],
      office,
      records: attendanceRecords as AttendanceRecordWithEmployee[],
      summary,
    };

    res.json({
      success: true,
      data: response,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /offices/{officeId}/attendance:
 *   post:
 *     tags: [Office Management]
 *     summary: Submit attendance
 *     description: Submit attendance records for employees/workers
 *     parameters:
 *       - in: path
 *         name: officeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SubmitAttendanceRequest'
 *     responses:
 *       201:
 *         description: Attendance submitted successfully
 */
router.post('/:officeId/attendance',
  authorize([], 'canManageOffice'),
  requireAction('create'),
  validateRequest(submitAttendanceSchema),
  asyncHandler(async (req, res) => {
    const { officeId } = req.params;
    const attendanceData: SubmitAttendanceRequest = req.body;
    const user = req.user!;

    // Check if office exists
    const office = await prisma.office.findUnique({
      where: { id: officeId },
    });

    if (!office) {
      throw new AppError('Office not found', 404, 'OFFICE_NOT_FOUND');
    }

    const { date, records } = attendanceData;
    const targetDate = new Date(date);

    let processed = 0;
    let failed = 0;
    const errors: string[] = [];

    // Process each attendance record
    for (const record of records) {
      try {
        // Validate that either employeeId or userId is provided
        if (!record.employeeId && !record.userId) {
          errors.push('Either employeeId or userId must be provided');
          failed++;
          continue;
        }

        // If employeeId is provided, verify employee exists
        if (record.employeeId) {
          const employee = await prisma.employee.findUnique({
            where: { id: record.employeeId },
          });

          if (!employee || employee.officeId !== officeId) {
            errors.push(`Employee ${record.employeeId} not found in this office`);
            failed++;
            continue;
          }
        }

        // If userId is provided, verify user exists
        if (record.userId) {
          const userExists = await prisma.user.findUnique({
            where: { id: record.userId },
          });

          if (!userExists) {
            errors.push(`User ${record.userId} not found`);
            failed++;
            continue;
          }
        }

        // Parse check-in and check-out times
        let checkInTime: Date | undefined;
        let checkOutTime: Date | undefined;

        if (record.checkInTime) {
          const [hours, minutes] = record.checkInTime.split(':').map(Number);
          checkInTime = new Date(targetDate);
          checkInTime.setHours(hours, minutes, 0, 0);
        }

        if (record.checkOutTime) {
          const [hours, minutes] = record.checkOutTime.split(':').map(Number);
          checkOutTime = new Date(targetDate);
          checkOutTime.setHours(hours, minutes, 0, 0);
        }

        // Calculate hours worked if both times are provided
        let hoursWorked = record.hoursWorked;
        if (checkInTime && checkOutTime && !hoursWorked) {
          const diffMs = checkOutTime.getTime() - checkInTime.getTime();
          hoursWorked = diffMs / (1000 * 60 * 60); // Convert to hours
        }

        // Create or update attendance record
        await prisma.attendanceRecord.upsert({
          where: {
            officeId_employeeId_date: record.employeeId ? {
              officeId,
              employeeId: record.employeeId,
              date: targetDate,
            } : undefined,
            officeId_userId_date: record.userId ? {
              officeId,
              userId: record.userId,
              date: targetDate,
            } : undefined,
          },
          update: {
            status: record.status,
            checkInTime,
            checkOutTime,
            hoursWorked,
            overtime: record.overtime,
            notes: record.notes,
            updatedAt: new Date(),
          },
          create: {
            officeId,
            employeeId: record.employeeId,
            userId: record.userId,
            date: targetDate,
            status: record.status,
            checkInTime,
            checkOutTime,
            hoursWorked,
            overtime: record.overtime,
            notes: record.notes,
          },
        });

        processed++;
      } catch (error) {
        console.error('Error processing attendance record:', error);
        errors.push(`Failed to process record: ${error instanceof Error ? error.message : 'Unknown error'}`);
        failed++;
      }
    }

    // Log activity
    await prisma.activity.create({
      data: {
        userId: user.id,
        action: 'SUBMIT_ATTENDANCE',
        description: `Submitted attendance for ${office.name} on ${date}`,
        metadata: {
          officeName: office.name,
          date,
          processed,
          failed,
        },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
      },
    });

    const response: AttendanceSubmissionResponse = {
      success: failed === 0,
      processed,
      failed,
      errors,
      message: failed === 0
        ? 'All attendance records submitted successfully'
        : `${processed} records processed, ${failed} failed`,
    };

    res.status(201).json({
      success: true,
      data: response,
      timestamp: new Date().toISOString(),
    });
  })
);

export default router;
