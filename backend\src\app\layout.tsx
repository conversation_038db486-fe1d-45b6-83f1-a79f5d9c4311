import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'SRSR Property Management API',
  description: 'Comprehensive API for SRSR Property Management System with RBAC, real-time updates, and multi-property management capabilities.',
  keywords: 'property management, API, real-time, RBAC, Next.js, Express',
  authors: [{ name: 'SRSR Property Management' }],
  robots: 'noindex, nofollow', // API server shouldn't be indexed
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        {children}
      </body>
    </html>
  );
}
