import { NextRequest, NextResponse } from 'next/server';
import express from 'express';
import { createServer } from 'http';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';

// Import middleware
import { errorHand<PERSON>, notFoundHandler } from '@/middleware/errorHandler';
import { endpointSpecificRateLimit } from '@/middleware/rateLimiter';

// Import routes
import authRoutes from '@/routes/auth';
import propertiesRoutes from '@/routes/properties';
import dashboardRoutes from '@/routes/dashboard';
import officesRoutes from '@/routes/offices';

// Create Express app for API handling
const createExpressApp = () => {
  const app = express();

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: false, // Disable for API
    crossOriginEmbedderPolicy: false,
  }));

  // CORS configuration
  app.use(cors({
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  }));

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Compression middleware
  app.use(compression());

  // Logging middleware (only in development)
  if (process.env.NODE_ENV === 'development') {
    app.use(morgan('dev'));
  }

  // Rate limiting
  app.use(endpointSpecificRateLimit);

  // API routes
  app.use('/auth', authRoutes);
  app.use('/properties', propertiesRoutes);
  app.use('/dashboard', dashboardRoutes);
  app.use('/offices', officesRoutes);

  // 404 handler
  app.use(notFoundHandler);

  // Global error handler
  app.use(errorHandler);

  return app;
};

// Create Express app instance
const expressApp = createExpressApp();

// Convert Express app to handle Next.js requests
const handleRequest = async (req: NextRequest, context: { params: { slug: string[] } }) => {
  return new Promise<NextResponse>((resolve) => {
    // Create mock Express request and response objects
    const mockReq = {
      method: req.method,
      url: `/${context.params.slug.join('/')}${req.nextUrl.search}`,
      headers: Object.fromEntries(req.headers.entries()),
      body: req.body,
      ip: req.ip || '127.0.0.1',
      get: (header: string) => req.headers.get(header),
    } as any;

    const mockRes = {
      statusCode: 200,
      headers: new Map(),
      body: '',
      status: function(code: number) {
        this.statusCode = code;
        return this;
      },
      json: function(data: any) {
        this.body = JSON.stringify(data);
        this.headers.set('Content-Type', 'application/json');
        resolve(new NextResponse(this.body, {
          status: this.statusCode,
          headers: Object.fromEntries(this.headers),
        }));
        return this;
      },
      send: function(data: any) {
        this.body = typeof data === 'string' ? data : JSON.stringify(data);
        resolve(new NextResponse(this.body, {
          status: this.statusCode,
          headers: Object.fromEntries(this.headers),
        }));
        return this;
      },
      set: function(header: string, value: string) {
        this.headers.set(header, value);
        return this;
      },
      setHeader: function(header: string, value: string) {
        this.headers.set(header, value);
        return this;
      },
      getHeader: function(header: string) {
        return this.headers.get(header);
      },
      end: function(data?: any) {
        if (data) {
          this.body = typeof data === 'string' ? data : JSON.stringify(data);
        }
        resolve(new NextResponse(this.body, {
          status: this.statusCode,
          headers: Object.fromEntries(this.headers),
        }));
        return this;
      },
    } as any;

    // Handle request body for POST/PUT/PATCH requests
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      req.json().then((body) => {
        mockReq.body = body;
        expressApp(mockReq, mockRes);
      }).catch(() => {
        expressApp(mockReq, mockRes);
      });
    } else {
      expressApp(mockReq, mockRes);
    }
  });
};

// Export HTTP method handlers
export async function GET(req: NextRequest, context: { params: { slug: string[] } }) {
  return handleRequest(req, context);
}

export async function POST(req: NextRequest, context: { params: { slug: string[] } }) {
  return handleRequest(req, context);
}

export async function PUT(req: NextRequest, context: { params: { slug: string[] } }) {
  return handleRequest(req, context);
}

export async function PATCH(req: NextRequest, context: { params: { slug: string[] } }) {
  return handleRequest(req, context);
}

export async function DELETE(req: NextRequest, context: { params: { slug: string[] } }) {
  return handleRequest(req, context);
}

export async function OPTIONS(req: NextRequest, context: { params: { slug: string[] } }) {
  // Handle CORS preflight requests
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.CORS_ORIGIN?.split(',')[0] || 'http://localhost:3000',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}
