import { Router } from 'express';
import { UserRole } from '@prisma/client';
import { prisma } from '@/lib/prisma';
import { validateRequest, asyncHandler, AppError } from '@/middleware/errorHandler';
import { authenticate, authorize } from '@/middleware/auth';
import { generalRateLimit } from '@/middleware/rateLimiter';
import { z } from 'zod';

const router = Router();

// Apply authentication and rate limiting
router.use(authenticate);
router.use(generalRateLimit);

// Report query schema
const reportQuerySchema = z.object({
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date must be in YYYY-MM-DD format'),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date must be in YYYY-MM-DD format'),
  propertyIds: z.string().optional(),
  format: z.enum(['json', 'csv']).default('json'),
});

/**
 * @swagger
 * /reports/properties:
 *   get:
 *     tags: [Reports]
 *     summary: Generate property report
 *     description: Generate comprehensive property performance report
 *     parameters:
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: propertyIds
 *         schema:
 *           type: string
 *           description: Comma-separated property IDs
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, csv]
 *           default: json
 *     responses:
 *       200:
 *         description: Property report generated successfully
 */
router.get('/properties',
  authorize([], 'canViewReports'),
  validateRequest(reportQuerySchema, 'query'),
  asyncHandler(async (req, res) => {
    const { startDate, endDate, propertyIds, format } = req.query as any;
    const user = req.user!;

    const start = new Date(startDate);
    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999);

    // Build property filter based on RBAC
    let propertyFilter: any = {};
    if (user.role !== UserRole.SUPER_ADMIN) {
      propertyFilter.id = { in: user.assignedProperties };
    }

    if (propertyIds) {
      const requestedPropertyIds = propertyIds.split(',');
      if (user.role !== UserRole.SUPER_ADMIN) {
        const allowedPropertyIds = requestedPropertyIds.filter(id => 
          user.assignedProperties.includes(id)
        );
        propertyFilter.id = { in: allowedPropertyIds };
      } else {
        propertyFilter.id = { in: requestedPropertyIds };
      }
    }

    // Get properties with system statuses and activities
    const properties = await prisma.property.findMany({
      where: propertyFilter,
      include: {
        systemStatuses: true,
        activities: {
          where: {
            createdAt: {
              gte: start,
              lte: end,
            },
          },
        },
        alerts: {
          where: {
            createdAt: {
              gte: start,
              lte: end,
            },
          },
        },
      },
    });

    // Generate report data
    const reportData = properties.map(property => {
      const systemStatuses = property.systemStatuses;
      const activities = property.activities;
      const alerts = property.alerts;

      const totalSystems = systemStatuses.length;
      const operationalSystems = systemStatuses.filter(s => s.status === 'OPERATIONAL').length;
      const warningSystems = systemStatuses.filter(s => s.status === 'WARNING').length;
      const criticalSystems = systemStatuses.filter(s => s.status === 'CRITICAL').length;
      const offlineSystems = systemStatuses.filter(s => s.status === 'OFFLINE').length;

      const avgHealthScore = totalSystems > 0
        ? systemStatuses.reduce((sum, status) => sum + (status.healthScore || 0), 0) / totalSystems
        : 100;

      const criticalAlerts = alerts.filter(a => a.severity === 'CRITICAL').length;
      const resolvedAlerts = alerts.filter(a => a.status === 'RESOLVED').length;

      return {
        propertyId: property.id,
        propertyName: property.name,
        propertyType: property.type,
        address: property.address,
        isActive: property.isActive,
        systemHealth: {
          totalSystems,
          operationalSystems,
          warningSystems,
          criticalSystems,
          offlineSystems,
          averageHealthScore: Math.round(avgHealthScore),
          uptime: Math.round((operationalSystems / totalSystems) * 100),
        },
        alerts: {
          total: alerts.length,
          critical: criticalAlerts,
          resolved: resolvedAlerts,
          resolutionRate: alerts.length > 0 ? Math.round((resolvedAlerts / alerts.length) * 100) : 0,
        },
        activities: {
          total: activities.length,
          systemUpdates: activities.filter(a => a.action === 'UPDATE_SYSTEM').length,
          maintenanceActivities: activities.filter(a => a.action.includes('MAINTENANCE')).length,
        },
        reportPeriod: {
          startDate,
          endDate,
        },
      };
    });

    if (format === 'csv') {
      // Generate CSV format
      const csvHeaders = [
        'Property ID', 'Property Name', 'Type', 'Address', 'Active',
        'Total Systems', 'Operational', 'Warning', 'Critical', 'Offline',
        'Avg Health Score', 'Uptime %', 'Total Alerts', 'Critical Alerts',
        'Resolved Alerts', 'Resolution Rate %', 'Total Activities'
      ];

      const csvRows = reportData.map(data => [
        data.propertyId,
        data.propertyName,
        data.propertyType,
        data.address,
        data.isActive,
        data.systemHealth.totalSystems,
        data.systemHealth.operationalSystems,
        data.systemHealth.warningSystems,
        data.systemHealth.criticalSystems,
        data.systemHealth.offlineSystems,
        data.systemHealth.averageHealthScore,
        data.systemHealth.uptime,
        data.alerts.total,
        data.alerts.critical,
        data.alerts.resolved,
        data.alerts.resolutionRate,
        data.activities.total,
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="property-report-${startDate}-to-${endDate}.csv"`);
      res.send(csvContent);
    } else {
      res.json({
        success: true,
        data: {
          reportType: 'property_performance',
          generatedAt: new Date().toISOString(),
          period: { startDate, endDate },
          properties: reportData,
          summary: {
            totalProperties: reportData.length,
            averageHealthScore: Math.round(
              reportData.reduce((sum, p) => sum + p.systemHealth.averageHealthScore, 0) / reportData.length
            ),
            totalAlerts: reportData.reduce((sum, p) => sum + p.alerts.total, 0),
            totalActivities: reportData.reduce((sum, p) => sum + p.activities.total, 0),
          },
        },
        timestamp: new Date().toISOString(),
      });
    }
  })
);

/**
 * @swagger
 * /reports/attendance:
 *   get:
 *     tags: [Reports]
 *     summary: Generate attendance report
 *     description: Generate attendance report for offices
 *     parameters:
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: officeIds
 *         schema:
 *           type: string
 *           description: Comma-separated office IDs
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, csv]
 *           default: json
 *     responses:
 *       200:
 *         description: Attendance report generated successfully
 */
router.get('/attendance',
  authorize([], 'canViewReports'),
  validateRequest(reportQuerySchema, 'query'),
  asyncHandler(async (req, res) => {
    const { startDate, endDate, officeIds, format } = req.query as any;

    const start = new Date(startDate);
    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999);

    // Build office filter
    let officeFilter: any = { isActive: true };
    if (officeIds) {
      officeFilter.id = { in: officeIds.split(',') };
    }

    // Get offices with attendance data
    const offices = await prisma.office.findMany({
      where: officeFilter,
      include: {
        attendanceRecords: {
          where: {
            date: {
              gte: start,
              lte: end,
            },
          },
          include: {
            employee: {
              select: {
                id: true,
                name: true,
                employeeId: true,
                designation: true,
              },
            },
          },
        },
        employees: {
          where: { isActive: true },
        },
      },
    });

    // Generate attendance report
    const reportData = offices.map(office => {
      const attendanceRecords = office.attendanceRecords;
      const totalEmployees = office.employees.length;

      const totalRecords = attendanceRecords.length;
      const presentRecords = attendanceRecords.filter(r => r.status === 'PRESENT').length;
      const absentRecords = attendanceRecords.filter(r => r.status === 'ABSENT').length;
      const lateRecords = attendanceRecords.filter(r => r.status === 'LATE').length;
      const leaveRecords = attendanceRecords.filter(r => r.status === 'LEAVE').length;

      const attendanceRate = totalRecords > 0 ? Math.round((presentRecords / totalRecords) * 100) : 0;

      // Calculate average hours worked
      const totalHours = attendanceRecords.reduce((sum, record) => sum + (record.hoursWorked || 0), 0);
      const avgHoursWorked = totalRecords > 0 ? Math.round((totalHours / totalRecords) * 100) / 100 : 0;

      return {
        officeId: office.id,
        officeName: office.name,
        officeType: office.type,
        totalEmployees,
        attendance: {
          totalRecords,
          present: presentRecords,
          absent: absentRecords,
          late: lateRecords,
          leave: leaveRecords,
          attendanceRate,
          avgHoursWorked,
        },
        reportPeriod: {
          startDate,
          endDate,
        },
      };
    });

    if (format === 'csv') {
      const csvHeaders = [
        'Office ID', 'Office Name', 'Type', 'Total Employees',
        'Total Records', 'Present', 'Absent', 'Late', 'Leave',
        'Attendance Rate %', 'Avg Hours Worked'
      ];

      const csvRows = reportData.map(data => [
        data.officeId,
        data.officeName,
        data.officeType,
        data.totalEmployees,
        data.attendance.totalRecords,
        data.attendance.present,
        data.attendance.absent,
        data.attendance.late,
        data.attendance.leave,
        data.attendance.attendanceRate,
        data.attendance.avgHoursWorked,
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="attendance-report-${startDate}-to-${endDate}.csv"`);
      res.send(csvContent);
    } else {
      res.json({
        success: true,
        data: {
          reportType: 'attendance',
          generatedAt: new Date().toISOString(),
          period: { startDate, endDate },
          offices: reportData,
          summary: {
            totalOffices: reportData.length,
            totalEmployees: reportData.reduce((sum, o) => sum + o.totalEmployees, 0),
            overallAttendanceRate: Math.round(
              reportData.reduce((sum, o) => sum + o.attendance.attendanceRate, 0) / reportData.length
            ),
          },
        },
        timestamp: new Date().toISOString(),
      });
    }
  })
);

/**
 * @swagger
 * /reports/system-health:
 *   get:
 *     tags: [Reports]
 *     summary: Generate system health report
 *     description: Generate system health and maintenance report
 *     parameters:
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: systemType
 *         schema:
 *           type: string
 *           enum: [WATER, ELECTRICITY, SECURITY, INTERNET, OTT, MAINTENANCE]
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, csv]
 *           default: json
 *     responses:
 *       200:
 *         description: System health report generated successfully
 */
router.get('/system-health',
  authorize([], 'canViewReports'),
  asyncHandler(async (req, res) => {
    const { startDate, endDate, systemType, format } = req.query as any;
    const user = req.user!;

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Build property filter based on RBAC
    let propertyFilter: any = {};
    if (user.role !== UserRole.SUPER_ADMIN) {
      propertyFilter.id = { in: user.assignedProperties };
    }

    // Build system filter
    let systemFilter: any = {};
    if (systemType) {
      systemFilter.systemType = systemType;
    }

    // Get system statuses with historical data
    const systemStatuses = await prisma.systemStatus.findMany({
      where: {
        ...systemFilter,
        property: propertyFilter,
        updatedAt: {
          gte: start,
          lte: end,
        },
      },
      include: {
        property: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });

    // Group by system type and property
    const reportData = systemStatuses.reduce((acc, status) => {
      const key = `${status.propertyId}-${status.systemType}`;
      if (!acc[key]) {
        acc[key] = {
          propertyId: status.propertyId,
          propertyName: status.property.name,
          propertyType: status.property.type,
          systemType: status.systemType,
          currentStatus: status.status,
          currentHealthScore: status.healthScore,
          lastChecked: status.lastChecked,
          statusHistory: [],
        };
      }
      acc[key].statusHistory.push({
        status: status.status,
        healthScore: status.healthScore,
        timestamp: status.updatedAt,
        description: status.description,
      });
      return acc;
    }, {} as any);

    const reportArray = Object.values(reportData);

    res.json({
      success: true,
      data: {
        reportType: 'system_health',
        generatedAt: new Date().toISOString(),
        period: { startDate, endDate },
        systems: reportArray,
        summary: {
          totalSystems: reportArray.length,
          operationalSystems: reportArray.filter((s: any) => s.currentStatus === 'OPERATIONAL').length,
          warningSystems: reportArray.filter((s: any) => s.currentStatus === 'WARNING').length,
          criticalSystems: reportArray.filter((s: any) => s.currentStatus === 'CRITICAL').length,
          offlineSystems: reportArray.filter((s: any) => s.currentStatus === 'OFFLINE').length,
        },
      },
      timestamp: new Date().toISOString(),
    });
  })
);

export default router;
