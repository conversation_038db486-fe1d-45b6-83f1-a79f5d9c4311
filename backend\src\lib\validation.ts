import { z } from 'zod';

// Authentication Schemas
export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  deviceId: z.string().optional(),
  deviceName: z.string().optional(),
});

export const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token is required'),
});

// User Schemas
export const createUserSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email format'),
  phone: z.string().regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format'),
  role: z.enum(['SUPER_ADMIN', 'PROPERTY_MANAGER', 'OFFICE_MANAGER', 'SECURITY_PERSONNEL', 'MAINTENANCE_STAFF', 'CONSTRUCTION_SUPERVISOR']),
  assignedProperties: z.array(z.string().uuid()).optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
});

export const updateUserSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  phone: z.string().regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format').optional(),
  avatar: z.string().url('Invalid avatar URL').optional(),
  timezone: z.string().optional(),
  language: z.enum(['en', 'hi', 'te']).optional(),
});

// Property Schemas
export const createPropertySchema = z.object({
  name: z.string().min(2, 'Property name must be at least 2 characters'),
  type: z.enum(['RESIDENTIAL', 'OFFICE', 'CONSTRUCTION']),
  address: z.string().min(10, 'Address must be at least 10 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  latitude: z.number().min(-90).max(90).optional(),
  longitude: z.number().min(-180).max(180).optional(),
  images: z.array(z.string().url()).optional(),
});

export const updatePropertySchema = z.object({
  name: z.string().min(2, 'Property name must be at least 2 characters').optional(),
  address: z.string().min(10, 'Address must be at least 10 characters').optional(),
  description: z.string().min(10, 'Description must be at least 10 characters').optional(),
  latitude: z.number().min(-90).max(90).optional(),
  longitude: z.number().min(-180).max(180).optional(),
  images: z.array(z.string().url()).optional(),
  isActive: z.boolean().optional(),
});

export const updateSystemSchema = z.object({
  status: z.enum(['OPERATIONAL', 'WARNING', 'CRITICAL', 'OFFLINE']),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  healthScore: z.number().min(0).max(100).optional(),
});

// Office & Attendance Schemas
export const createOfficeSchema = z.object({
  name: z.string().min(2, 'Office name must be at least 2 characters'),
  type: z.enum(['OFFICE', 'CONSTRUCTION_SITE']),
  address: z.string().min(10, 'Address must be at least 10 characters'),
  latitude: z.number().min(-90).max(90).optional(),
  longitude: z.number().min(-180).max(180).optional(),
  workingHours: z.record(z.any()).optional(),
});

export const createEmployeeSchema = z.object({
  officeId: z.string().uuid('Invalid office ID'),
  name: z.string().min(2, 'Employee name must be at least 2 characters'),
  email: z.string().email('Invalid email format').optional(),
  phone: z.string().regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format').optional(),
  employeeId: z.string().min(1, 'Employee ID is required'),
  designation: z.string().min(2, 'Designation must be at least 2 characters'),
  department: z.string().optional(),
  joinDate: z.string().datetime('Invalid join date format'),
});

export const submitAttendanceSchema = z.object({
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
  records: z.array(z.object({
    employeeId: z.string().uuid().optional(),
    userId: z.string().uuid().optional(),
    status: z.enum(['PRESENT', 'ABSENT', 'LATE', 'HALF_DAY', 'LEAVE']),
    checkInTime: z.string().regex(/^\d{2}:\d{2}$/, 'Check-in time must be in HH:MM format').optional(),
    checkOutTime: z.string().regex(/^\d{2}:\d{2}$/, 'Check-out time must be in HH:MM format').optional(),
    hoursWorked: z.number().min(0).max(24).optional(),
    overtime: z.number().min(0).max(12).optional(),
    notes: z.string().optional(),
  })).min(1, 'At least one attendance record is required'),
});

// Alert Schemas
export const createAlertSchema = z.object({
  propertyId: z.string().uuid().optional(),
  title: z.string().min(5, 'Alert title must be at least 5 characters'),
  message: z.string().min(10, 'Alert message must be at least 10 characters'),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  category: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export const updateAlertSchema = z.object({
  status: z.enum(['OPEN', 'ACKNOWLEDGED', 'RESOLVED']).optional(),
  resolvedAt: z.string().datetime().optional(),
});

// Query Parameter Schemas
export const paginationSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
});

export const propertyQuerySchema = paginationSchema.extend({
  type: z.enum(['RESIDENTIAL', 'OFFICE', 'CONSTRUCTION']).optional(),
  status: z.enum(['OPERATIONAL', 'WARNING', 'CRITICAL', 'OFFLINE']).optional(),
  search: z.string().optional(),
});

export const alertQuerySchema = paginationSchema.extend({
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
  status: z.enum(['OPEN', 'ACKNOWLEDGED', 'RESOLVED']).optional(),
  propertyId: z.string().uuid().optional(),
});

export const attendanceQuerySchema = z.object({
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format').optional(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date must be in YYYY-MM-DD format').optional(),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date must be in YYYY-MM-DD format').optional(),
});

export const dashboardQuerySchema = z.object({
  propertyIds: z.string().optional(),
  timeRange: z.enum(['24h', '7d', '30d', '90d']).default('24h'),
});

// File Upload Schema
export const fileUploadSchema = z.object({
  fieldname: z.string(),
  originalname: z.string(),
  encoding: z.string(),
  mimetype: z.string().refine(
    (type) => ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'].includes(type),
    'Invalid file type. Only JPEG, PNG, WebP, and PDF files are allowed.'
  ),
  size: z.number().max(10 * 1024 * 1024, 'File size must be less than 10MB'),
});

// Validation Helper Functions
export function validateUUID(id: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePhone(phone: string): boolean {
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(phone);
}

export function validateDate(date: string): boolean {
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(date)) return false;
  
  const parsedDate = new Date(date);
  return parsedDate instanceof Date && !isNaN(parsedDate.getTime());
}

export function validateTime(time: string): boolean {
  const timeRegex = /^\d{2}:\d{2}$/;
  if (!timeRegex.test(time)) return false;
  
  const [hours, minutes] = time.split(':').map(Number);
  return hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59;
}
