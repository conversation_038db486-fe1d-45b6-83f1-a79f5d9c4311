// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Office _$OfficeFromJson(Map<String, dynamic> json) => Office(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      type: json['type'] as String,
      isActive: json['isActive'] as bool,
      employees: (json['employees'] as List<dynamic>)
          .map((e) => Employee.fromJson(e as Map<String, dynamic>))
          .toList(),
      settings:
          OfficeSettings.fromJson(json['settings'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$<PERSON>(Office instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'address': instance.address,
      'type': instance.type,
      'isActive': instance.isActive,
      'employees': instance.employees,
      'settings': instance.settings,
    };

Employee _$EmployeeFromJson(Map<String, dynamic> json) => Employee(
      id: json['id'] as String,
      name: json['name'] as String,
      mobile: json['mobile'] as String,
      role: json['role'] as String,
      dutyStartTime: json['dutyStartTime'] as String,
      dutyEndTime: json['dutyEndTime'] as String,
      team: json['team'] as String?,
      remarks: json['remarks'] as String?,
      isActive: json['isActive'] as bool,
      joinedDate: DateTime.parse(json['joinedDate'] as String),
    );

Map<String, dynamic> _$EmployeeToJson(Employee instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'mobile': instance.mobile,
      'role': instance.role,
      'dutyStartTime': instance.dutyStartTime,
      'dutyEndTime': instance.dutyEndTime,
      'team': instance.team,
      'remarks': instance.remarks,
      'isActive': instance.isActive,
      'joinedDate': instance.joinedDate.toIso8601String(),
    };

Attendance _$AttendanceFromJson(Map<String, dynamic> json) => Attendance(
      id: json['id'] as String,
      employeeId: json['employeeId'] as String,
      officeId: json['officeId'] as String,
      date: DateTime.parse(json['date'] as String),
      status: json['status'] as String,
      checkInTime: json['checkInTime'] as String?,
      checkOutTime: json['checkOutTime'] as String?,
      hoursWorked: (json['hoursWorked'] as num).toInt(),
      remarks: json['remarks'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$AttendanceToJson(Attendance instance) =>
    <String, dynamic>{
      'id': instance.id,
      'employeeId': instance.employeeId,
      'officeId': instance.officeId,
      'date': instance.date.toIso8601String(),
      'status': instance.status,
      'checkInTime': instance.checkInTime,
      'checkOutTime': instance.checkOutTime,
      'hoursWorked': instance.hoursWorked,
      'remarks': instance.remarks,
      'createdAt': instance.createdAt.toIso8601String(),
    };

ConstructionSite _$ConstructionSiteFromJson(Map<String, dynamic> json) =>
    ConstructionSite(
      id: json['id'] as String,
      name: json['name'] as String,
      location: json['location'] as String,
      status: json['status'] as String,
      workers: (json['workers'] as List<dynamic>)
          .map((e) => Worker.fromJson(e as Map<String, dynamic>))
          .toList(),
      startDate: DateTime.parse(json['startDate'] as String),
      expectedEndDate: json['expectedEndDate'] == null
          ? null
          : DateTime.parse(json['expectedEndDate'] as String),
    );

Map<String, dynamic> _$ConstructionSiteToJson(ConstructionSite instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'location': instance.location,
      'status': instance.status,
      'workers': instance.workers,
      'startDate': instance.startDate.toIso8601String(),
      'expectedEndDate': instance.expectedEndDate?.toIso8601String(),
    };

Worker _$WorkerFromJson(Map<String, dynamic> json) => Worker(
      id: json['id'] as String,
      name: json['name'] as String,
      role: json['role'] as String,
      dutyStartTime: json['dutyStartTime'] as String,
      dutyEndTime: json['dutyEndTime'] as String,
      mobile: json['mobile'] as String?,
      shift: json['shift'] as String,
      isActive: json['isActive'] as bool,
    );

Map<String, dynamic> _$WorkerToJson(Worker instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'role': instance.role,
      'dutyStartTime': instance.dutyStartTime,
      'dutyEndTime': instance.dutyEndTime,
      'mobile': instance.mobile,
      'shift': instance.shift,
      'isActive': instance.isActive,
    };

WorkerAttendance _$WorkerAttendanceFromJson(Map<String, dynamic> json) =>
    WorkerAttendance(
      id: json['id'] as String,
      workerId: json['workerId'] as String,
      siteId: json['siteId'] as String,
      date: DateTime.parse(json['date'] as String),
      status: json['status'] as String,
      hoursWorked: (json['hoursWorked'] as num).toInt(),
      remarks: json['remarks'] as String?,
    );

Map<String, dynamic> _$WorkerAttendanceToJson(WorkerAttendance instance) =>
    <String, dynamic>{
      'id': instance.id,
      'workerId': instance.workerId,
      'siteId': instance.siteId,
      'date': instance.date.toIso8601String(),
      'status': instance.status,
      'hoursWorked': instance.hoursWorked,
      'remarks': instance.remarks,
    };

OfficeSettings _$OfficeSettingsFromJson(Map<String, dynamic> json) =>
    OfficeSettings(
      officeId: json['officeId'] as String,
      workingDaysPattern: json['workingDaysPattern'] as String,
      defaultStartTime: json['defaultStartTime'] as String,
      defaultEndTime: json['defaultEndTime'] as String,
      allowFlexibleTiming: json['allowFlexibleTiming'] as bool,
      gracePeriodMinutes: (json['gracePeriodMinutes'] as num).toInt(),
      requireCheckOut: json['requireCheckOut'] as bool,
    );

Map<String, dynamic> _$OfficeSettingsToJson(OfficeSettings instance) =>
    <String, dynamic>{
      'officeId': instance.officeId,
      'workingDaysPattern': instance.workingDaysPattern,
      'defaultStartTime': instance.defaultStartTime,
      'defaultEndTime': instance.defaultEndTime,
      'allowFlexibleTiming': instance.allowFlexibleTiming,
      'gracePeriodMinutes': instance.gracePeriodMinutes,
      'requireCheckOut': instance.requireCheckOut,
    };

AttendanceReport _$AttendanceReportFromJson(Map<String, dynamic> json) =>
    AttendanceReport(
      officeId: json['officeId'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      reportType: json['reportType'] as String,
      summaries: (json['summaries'] as List<dynamic>)
          .map((e) => AttendanceSummary.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalWorkingDays: (json['totalWorkingDays'] as num).toInt(),
      totalEmployees: (json['totalEmployees'] as num).toInt(),
    );

Map<String, dynamic> _$AttendanceReportToJson(AttendanceReport instance) =>
    <String, dynamic>{
      'officeId': instance.officeId,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'reportType': instance.reportType,
      'summaries': instance.summaries,
      'totalWorkingDays': instance.totalWorkingDays,
      'totalEmployees': instance.totalEmployees,
    };

AttendanceSummary _$AttendanceSummaryFromJson(Map<String, dynamic> json) =>
    AttendanceSummary(
      employeeId: json['employeeId'] as String,
      employeeName: json['employeeName'] as String,
      presentDays: (json['presentDays'] as num).toInt(),
      absentDays: (json['absentDays'] as num).toInt(),
      lateDays: (json['lateDays'] as num).toInt(),
      halfDays: (json['halfDays'] as num).toInt(),
      attendancePercentage: (json['attendancePercentage'] as num).toDouble(),
      totalHoursWorked: (json['totalHoursWorked'] as num).toInt(),
    );

Map<String, dynamic> _$AttendanceSummaryToJson(AttendanceSummary instance) =>
    <String, dynamic>{
      'employeeId': instance.employeeId,
      'employeeName': instance.employeeName,
      'presentDays': instance.presentDays,
      'absentDays': instance.absentDays,
      'lateDays': instance.lateDays,
      'halfDays': instance.halfDays,
      'attendancePercentage': instance.attendancePercentage,
      'totalHoursWorked': instance.totalHoursWorked,
    };
