// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id                String   @id @default(uuid())
  name              String
  email             String   @unique
  phone             String
  password          String
  role              UserRole
  isActive          Boolean  @default(true)
  avatar            String?
  timezone          String   @default("Asia/Kolkata")
  language          String   @default("en")
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  lastLogin         DateTime?
  
  // Relationships
  assignedProperties UserProperty[]
  attendanceRecords  AttendanceRecord[]
  activities         Activity[]
  refreshTokens      RefreshToken[]
  
  @@map("users")
}

model RefreshToken {
  id        String   @id @default(uuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("refresh_tokens")
}

// Property Management
model Property {
  id          String      @id @default(uuid())
  name        String
  type        PropertyType
  address     String
  description String
  isActive    Boolean     @default(true)
  latitude    Float?
  longitude   Float?
  images      String[]    @default([])
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  // Relationships
  assignedUsers  UserProperty[]
  systemStatuses SystemStatus[]
  activities     Activity[]
  alerts         Alert[]
  
  @@map("properties")
}

model UserProperty {
  id         String @id @default(uuid())
  userId     String
  propertyId String
  
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  
  @@unique([userId, propertyId])
  @@map("user_properties")
}

// System Monitoring
model SystemStatus {
  id          String     @id @default(uuid())
  propertyId  String
  systemType  SystemType
  status      SystemStatusEnum
  description String?
  metadata    Json?      @default("{}")
  healthScore Float?     @default(100)
  lastChecked DateTime   @default(now())
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  
  @@unique([propertyId, systemType])
  @@map("system_statuses")
}

// Office Management
model Office {
  id          String     @id @default(uuid())
  name        String
  type        OfficeType
  address     String
  latitude    Float?
  longitude   Float?
  isActive    Boolean    @default(true)
  workingHours Json?     @default("{}")
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  
  // Relationships
  attendanceRecords AttendanceRecord[]
  employees         Employee[]
  
  @@map("offices")
}

model Employee {
  id           String   @id @default(uuid())
  officeId     String
  name         String
  email        String?
  phone        String?
  employeeId   String   @unique
  designation  String
  department   String?
  isActive     Boolean  @default(true)
  joinDate     DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  office            Office             @relation(fields: [officeId], references: [id], onDelete: Cascade)
  attendanceRecords AttendanceRecord[]
  
  @@map("employees")
}

model AttendanceRecord {
  id           String            @id @default(uuid())
  officeId     String
  employeeId   String?
  userId       String?
  date         DateTime
  status       AttendanceStatus
  checkInTime  DateTime?
  checkOutTime DateTime?
  hoursWorked  Float?           @default(0)
  overtime     Float?           @default(0)
  notes        String?
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt
  
  office   Office    @relation(fields: [officeId], references: [id], onDelete: Cascade)
  employee Employee? @relation(fields: [employeeId], references: [id], onDelete: SetNull)
  user     User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  @@unique([officeId, employeeId, date])
  @@unique([officeId, userId, date])
  @@map("attendance_records")
}

// Alerts and Notifications
model Alert {
  id          String      @id @default(uuid())
  propertyId  String?
  title       String
  message     String
  severity    AlertSeverity
  status      AlertStatus @default(OPEN)
  category    String?
  metadata    Json?       @default("{}")
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  resolvedAt  DateTime?
  
  property Property? @relation(fields: [propertyId], references: [id], onDelete: SetNull)
  
  @@map("alerts")
}

// Activity Logging
model Activity {
  id          String   @id @default(uuid())
  userId      String?
  propertyId  String?
  action      String
  description String
  metadata    Json?    @default("{}")
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())
  
  user     User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  property Property? @relation(fields: [propertyId], references: [id], onDelete: SetNull)
  
  @@map("activities")
}

// Enums
enum UserRole {
  SUPER_ADMIN
  PROPERTY_MANAGER
  OFFICE_MANAGER
  SECURITY_PERSONNEL
  MAINTENANCE_STAFF
  CONSTRUCTION_SUPERVISOR
}

enum PropertyType {
  RESIDENTIAL
  OFFICE
  CONSTRUCTION
}

enum SystemType {
  WATER
  ELECTRICITY
  SECURITY
  INTERNET
  OTT
  MAINTENANCE
}

enum SystemStatusEnum {
  OPERATIONAL
  WARNING
  CRITICAL
  OFFLINE
}

enum OfficeType {
  OFFICE
  CONSTRUCTION_SITE
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  HALF_DAY
  LEAVE
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AlertStatus {
  OPEN
  ACKNOWLEDGED
  RESOLVED
}
