import { User, Property, SystemStatus, Office, AttendanceRecord, Alert, Activity } from '@prisma/client';

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
  path?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
  deviceId?: string;
  deviceName?: string;
}

export interface LoginResponse {
  user: UserWithPermissions;
  token: AuthToken;
  message: string;
}

export interface AuthToken {
  accessToken: string;
  refreshToken: string;
  expiresAt: string;
  tokenType: 'Bearer';
}

export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  assignedProperties: string[];
  iat?: number;
  exp?: number;
}

// User Types
export interface UserPermissions {
  canViewDashboard: boolean;
  canManageProperties: boolean;
  canManageOffice: boolean;
  canManageSecurity: boolean;
  canManageMaintenance: boolean;
  canManageUsers: boolean;
  canViewReports: boolean;
  canExportData: boolean;
  allowedScreens: string[];
  allowedActions: ('create' | 'read' | 'update' | 'delete' | 'export' | 'import')[];
}

export interface UserWithPermissions extends Omit<User, 'password'> {
  assignedProperties: string[];
  permissions: UserPermissions;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  phone: string;
  role: string;
  assignedProperties?: string[];
  password?: string;
}

export interface UpdateUserRequest {
  name?: string;
  phone?: string;
  avatar?: string;
  timezone?: string;
  language?: string;
}

// Property Types
export interface PropertyDetail extends Property {
  systemStatuses: SystemStatusSummary[];
  settings?: PropertySettings;
  recentActivities: Activity[];
  statistics: PropertyStatistics;
}

export interface SystemStatusSummary {
  systemType: string;
  status: string;
  description?: string;
  healthScore?: number;
  lastChecked: string;
}

export interface PropertySettings {
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  monitoring: {
    interval: number;
    alertThresholds: Record<string, number>;
  };
  access: {
    allowedUsers: string[];
    restrictedAreas: string[];
  };
}

export interface PropertyStatistics {
  totalSystems: number;
  operationalSystems: number;
  warningsSystems: number;
  criticalSystems: number;
  offlineSystems: number;
  averageHealthScore: number;
  uptime: number;
  lastIncident?: string;
}

export interface CreatePropertyRequest {
  name: string;
  type: 'RESIDENTIAL' | 'OFFICE' | 'CONSTRUCTION';
  address: string;
  description: string;
  latitude?: number;
  longitude?: number;
  images?: string[];
}

export interface UpdateSystemRequest {
  status: 'OPERATIONAL' | 'WARNING' | 'CRITICAL' | 'OFFLINE';
  description?: string;
  metadata?: Record<string, any>;
  healthScore?: number;
}

// Dashboard Types
export interface DashboardOverview {
  summary: {
    totalProperties: number;
    activeProperties: number;
    totalAlerts: number;
    criticalAlerts: number;
    systemHealth: number;
  };
  properties: PropertySummary[];
  recentAlerts: Alert[];
  systemStatuses: SystemStatusOverview[];
  activities: Activity[];
  statistics: DashboardStatistics;
}

export interface PropertySummary {
  id: string;
  name: string;
  type: string;
  status: string;
  healthScore: number;
  alertCount: number;
  lastUpdate: string;
}

export interface SystemStatusOverview {
  systemType: string;
  operational: number;
  warning: number;
  critical: number;
  offline: number;
  total: number;
}

export interface DashboardStatistics {
  timeRange: string;
  metrics: {
    uptime: number;
    incidents: number;
    resolved: number;
    avgResponseTime: number;
  };
  trends: {
    date: string;
    value: number;
  }[];
}

// Office & Attendance Types
export interface OfficeWithStats extends Office {
  employeeCount: number;
  presentToday: number;
  attendanceRate: number;
}

export interface AttendanceResponse {
  date: string;
  office: Office;
  records: AttendanceRecordWithEmployee[];
  summary: AttendanceSummary;
}

export interface AttendanceRecordWithEmployee extends AttendanceRecord {
  employee?: {
    id: string;
    name: string;
    employeeId: string;
    designation: string;
  };
  user?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface AttendanceSummary {
  total: number;
  present: number;
  absent: number;
  late: number;
  halfDay: number;
  leave: number;
  attendanceRate: number;
}

export interface SubmitAttendanceRequest {
  date: string;
  records: {
    employeeId?: string;
    userId?: string;
    status: 'PRESENT' | 'ABSENT' | 'LATE' | 'HALF_DAY' | 'LEAVE';
    checkInTime?: string;
    checkOutTime?: string;
    hoursWorked?: number;
    overtime?: number;
    notes?: string;
  }[];
}

export interface AttendanceSubmissionResponse {
  success: boolean;
  processed: number;
  failed: number;
  errors: string[];
  message: string;
}

// Error Types
export interface ErrorResponse {
  error: string;
  message: string;
  timestamp: string;
  path?: string;
  details?: Record<string, any>;
  retryAfter?: number;
}

// WebSocket Types
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
  userId?: string;
  propertyId?: string;
}

// File Upload Types
export interface FileUploadResponse {
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
  uploadedAt: string;
}

// Health Check Types
export interface HealthResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
}

export interface DetailedHealthResponse extends HealthResponse {
  services: {
    database: ServiceHealth;
    redis?: ServiceHealth;
    storage: ServiceHealth;
  };
  metrics: {
    memoryUsage: number;
    cpuUsage: number;
    activeConnections: number;
  };
}

export interface ServiceHealth {
  status: 'healthy' | 'unhealthy';
  responseTime: number;
  lastCheck: string;
  error?: string;
}
