// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Property _$PropertyFromJson(Map<String, dynamic> json) => Property(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      address: json['address'] as String,
      description: json['description'] as String,
      isActive: json['isActive'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      systemStatuses: (json['systemStatuses'] as List<dynamic>)
          .map((e) => SystemStatus.fromJson(e as Map<String, dynamic>))
          .toList(),
      settings: json['settings'] == null
          ? null
          : PropertySettings.fromJson(json['settings'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PropertyToJson(Property instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'address': instance.address,
      'description': instance.description,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'systemStatuses': instance.systemStatuses,
      'settings': instance.settings,
    };

SystemStatus _$SystemStatusFromJson(Map<String, dynamic> json) => SystemStatus(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      systemType: json['systemType'] as String,
      status: json['status'] as String,
      description: json['description'] as String?,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$SystemStatusToJson(SystemStatus instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'systemType': instance.systemType,
      'status': instance.status,
      'description': instance.description,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'metadata': instance.metadata,
    };

PropertySettings _$PropertySettingsFromJson(Map<String, dynamic> json) =>
    PropertySettings(
      propertyId: json['propertyId'] as String,
      waterSettings: json['waterSettings'] as Map<String, dynamic>,
      electricitySettings: json['electricitySettings'] as Map<String, dynamic>,
      securitySettings: json['securitySettings'] as Map<String, dynamic>,
      internetSettings: json['internetSettings'] as Map<String, dynamic>,
      ottSettings: json['ottSettings'] as Map<String, dynamic>,
      maintenanceSettings: json['maintenanceSettings'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$PropertySettingsToJson(PropertySettings instance) =>
    <String, dynamic>{
      'propertyId': instance.propertyId,
      'waterSettings': instance.waterSettings,
      'electricitySettings': instance.electricitySettings,
      'securitySettings': instance.securitySettings,
      'internetSettings': instance.internetSettings,
      'ottSettings': instance.ottSettings,
      'maintenanceSettings': instance.maintenanceSettings,
    };

WaterSystem _$WaterSystemFromJson(Map<String, dynamic> json) => WaterSystem(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      municipalConnections: (json['municipalConnections'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      borewellInfo: json['borewellInfo'] == null
          ? null
          : BorewellInfo.fromJson(json['borewellInfo'] as Map<String, dynamic>),
      tankInfo:
          WaterTankInfo.fromJson(json['tankInfo'] as Map<String, dynamic>),
      automationSystem: json['automationSystem'] == null
          ? null
          : AutomationSystem.fromJson(
              json['automationSystem'] as Map<String, dynamic>),
      contacts: (json['contacts'] as List<dynamic>)
          .map((e) => ContactInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WaterSystemToJson(WaterSystem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'municipalConnections': instance.municipalConnections,
      'borewellInfo': instance.borewellInfo,
      'tankInfo': instance.tankInfo,
      'automationSystem': instance.automationSystem,
      'contacts': instance.contacts,
    };

BorewellInfo _$BorewellInfoFromJson(Map<String, dynamic> json) => BorewellInfo(
      id: json['id'] as String,
      depth: (json['depth'] as num).toDouble(),
      waterLevel: (json['waterLevel'] as num).toDouble(),
      quality: json['quality'] as String,
      lastTested: DateTime.parse(json['lastTested'] as String),
    );

Map<String, dynamic> _$BorewellInfoToJson(BorewellInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'depth': instance.depth,
      'waterLevel': instance.waterLevel,
      'quality': instance.quality,
      'lastTested': instance.lastTested.toIso8601String(),
    };

WaterTankInfo _$WaterTankInfoFromJson(Map<String, dynamic> json) =>
    WaterTankInfo(
      id: json['id'] as String,
      capacity: (json['capacity'] as num).toDouble(),
      currentLevel: (json['currentLevel'] as num).toDouble(),
      material: json['material'] as String,
      lastCleaned: DateTime.parse(json['lastCleaned'] as String),
    );

Map<String, dynamic> _$WaterTankInfoToJson(WaterTankInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'capacity': instance.capacity,
      'currentLevel': instance.currentLevel,
      'material': instance.material,
      'lastCleaned': instance.lastCleaned.toIso8601String(),
    };

AutomationSystem _$AutomationSystemFromJson(Map<String, dynamic> json) =>
    AutomationSystem(
      id: json['id'] as String,
      brand: json['brand'] as String,
      model: json['model'] as String,
      isActive: json['isActive'] as bool,
      lastMaintenance: DateTime.parse(json['lastMaintenance'] as String),
    );

Map<String, dynamic> _$AutomationSystemToJson(AutomationSystem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'brand': instance.brand,
      'model': instance.model,
      'isActive': instance.isActive,
      'lastMaintenance': instance.lastMaintenance.toIso8601String(),
    };

ContactInfo _$ContactInfoFromJson(Map<String, dynamic> json) => ContactInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      phone: json['phone'] as String,
      email: json['email'] as String?,
      role: json['role'] as String,
      category: json['category'] as String,
    );

Map<String, dynamic> _$ContactInfoToJson(ContactInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phone': instance.phone,
      'email': instance.email,
      'role': instance.role,
      'category': instance.category,
    };
