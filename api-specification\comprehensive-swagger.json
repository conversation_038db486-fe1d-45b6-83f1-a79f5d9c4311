{"openapi": "3.0.3", "info": {"title": "SRSR Property Management API", "description": "Comprehensive API for SRSR Property Management System with RBAC, real-time updates, and multi-property management capabilities. This API supports the complete Flutter mobile application with all features including property management, office management, construction sites, attendance tracking, and system monitoring.", "version": "1.0.0", "contact": {"name": "SRSR Property Management", "email": "<EMAIL>", "url": "https://srsrproperty.com"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "termsOfService": "https://srsrproperty.com/terms"}, "servers": [{"url": "https://api.srsrproperty.com/v1", "description": "Production server"}, {"url": "https://staging-api.srsrproperty.com/v1", "description": "Staging server"}, {"url": "http://localhost:3000/v1", "description": "Development server"}], "security": [{"bearerAuth": []}], "tags": [{"name": "Authentication", "description": "User authentication and authorization"}, {"name": "Users", "description": "User management and RBAC"}, {"name": "Properties", "description": "Property management and system monitoring"}, {"name": "Dashboard", "description": "Dashboard data and analytics"}, {"name": "Office Management", "description": "Office locations and attendance management"}, {"name": "Construction Sites", "description": "Construction site management and worker tracking"}, {"name": "Maintenance", "description": "Maintenance issues and work orders"}, {"name": "Security", "description": "Security systems and monitoring"}, {"name": "Reports", "description": "Report generation and analytics"}, {"name": "Notifications", "description": "Push notifications and alerts"}, {"name": "WebSocket", "description": "Real-time updates via WebSocket"}], "paths": {"/auth/login": {"post": {"tags": ["Authentication"], "summary": "User login", "description": "Authenticate user and return JWT token with user permissions and assigned properties", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}, "examples": {"superAdmin": {"summary": "Super Admin Login", "value": {"email": "<EMAIL>", "password": "admin123", "deviceId": "device-123", "deviceName": "iPhone 15 Pro"}}, "propertyManager": {"summary": "Property Manager <PERSON><PERSON>", "value": {"email": "<EMAIL>", "password": "manager123", "deviceId": "device-456", "deviceName": "Samsung Galaxy S24"}}}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}, "examples": {"successfulLogin": {"summary": "Successful login response", "value": {"user": {"id": "123e4567-e89b-12d3-a456-426614174000", "name": "<PERSON>", "email": "<EMAIL>", "phone": "+919999999999", "role": "super_admin", "assignedProperties": ["prop-1", "prop-2"], "isActive": true, "permissions": {"canViewDashboard": true, "canManageProperties": true, "canManageOffice": true, "canManageSecurity": true, "canManageMaintenance": true, "canManageUsers": true, "canViewReports": true, "canExportData": true, "allowedScreens": ["dashboard", "properties", "office_management", "settings"], "allowedActions": ["create", "read", "update", "delete", "export"]}}, "token": {"accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "expiresAt": "2024-12-31T23:59:59Z", "tokenType": "Bearer"}, "message": "Login successful"}}}}}}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"invalidCredentials": {"summary": "Invalid email or password", "value": {"error": "INVALID_CREDENTIALS", "message": "Invalid email or password", "timestamp": "2024-01-01T12:00:00Z", "path": "/auth/login"}}}}}}, "429": {"description": "Too many login attempts", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"rateLimited": {"summary": "Rate limit exceeded", "value": {"error": "RATE_LIMIT_EXCEEDED", "message": "Too many login attempts. Please try again in 15 minutes.", "timestamp": "2024-01-01T12:00:00Z", "path": "/auth/login", "retryAfter": 900}}}}}}}}}, "/auth/refresh": {"post": {"tags": ["Authentication"], "summary": "Refresh access token", "description": "Refresh JWT token using refresh token", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"refreshToken": {"type": "string", "description": "Valid refresh token"}}, "required": ["refreshToken"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthToken"}}}}, "401": {"description": "Invalid refresh token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/auth/logout": {"post": {"tags": ["Authentication"], "summary": "User logout", "description": "Invalidate user session and tokens", "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Logout successful"}}}}}}}}}, "/users/profile": {"get": {"tags": ["Users"], "summary": "Get current user profile", "description": "Retrieve current authenticated user's profile information", "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Users"], "summary": "Update user profile", "description": "Update current user's profile information", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "Profile updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/properties": {"get": {"tags": ["Properties"], "summary": "List properties", "description": "Get paginated list of properties with filtering and RBAC. Users only see properties they have access to based on their role and assignments.", "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"name": "type", "in": "query", "description": "Filter by property type", "schema": {"$ref": "#/components/schemas/PropertyType"}}, {"name": "status", "in": "query", "description": "Filter by property status", "schema": {"$ref": "#/components/schemas/SystemStatus"}}, {"name": "search", "in": "query", "description": "Search by property name or address", "schema": {"type": "string"}}], "responses": {"200": {"description": "Properties retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedPropertiesResponse"}}}}}}, "post": {"tags": ["Properties"], "summary": "Create new property", "description": "Create a new property (Admin/Property Manager only)", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePropertyRequest"}}}}, "responses": {"201": {"description": "Property created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Property"}}}}}}}, "/properties/{propertyId}": {"get": {"tags": ["Properties"], "summary": "Get property details", "description": "Retrieve detailed property information including system statuses", "parameters": [{"$ref": "#/components/parameters/PropertyIdParam"}], "responses": {"200": {"description": "Property retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PropertyDetail"}}}}}}}, "/dashboard/overview": {"get": {"tags": ["Dashboard"], "summary": "Get dashboard overview", "description": "Retrieve comprehensive dashboard data with system health and statistics", "parameters": [{"name": "propertyIds", "in": "query", "description": "Filter by specific properties (comma-separated)", "schema": {"type": "string"}}, {"name": "timeRange", "in": "query", "description": "Time range for statistics", "schema": {"type": "string", "enum": ["24h", "7d", "30d", "90d"], "default": "24h"}}], "responses": {"200": {"description": "Dashboard overview retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DashboardOverview"}}}}}}}, "/offices/{officeId}/attendance": {"get": {"tags": ["Office Management"], "summary": "Get attendance records", "description": "Retrieve attendance records for an office", "parameters": [{"name": "officeId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "date", "in": "query", "description": "Specific date (YYYY-MM-DD)", "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Attendance records retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttendanceResponse"}}}}}}, "post": {"tags": ["Office Management"], "summary": "Submit attendance", "description": "Submit attendance records for employees/workers", "parameters": [{"name": "officeId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitAttendanceRequest"}}}}, "responses": {"201": {"description": "Attendance submitted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttendanceSubmissionResponse"}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token for authentication. Include in Authorization header as 'Bearer {token}'"}}, "parameters": {"PageParam": {"name": "page", "in": "query", "description": "Page number for pagination", "schema": {"type": "integer", "minimum": 1, "default": 1}}, "LimitParam": {"name": "limit", "in": "query", "description": "Number of items per page", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, "PropertyIdParam": {"name": "propertyId", "in": "path", "required": true, "description": "Property ID", "schema": {"type": "string", "format": "uuid"}}}, "schemas": {"LoginRequest": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "description": "User email address"}, "password": {"type": "string", "minLength": 6, "description": "User password"}, "deviceId": {"type": "string", "description": "Device identifier for tracking"}, "deviceName": {"type": "string", "description": "Human-readable device name"}}}, "LoginResponse": {"type": "object", "required": ["user", "token", "message"], "properties": {"user": {"$ref": "#/components/schemas/User"}, "token": {"$ref": "#/components/schemas/AuthToken"}, "message": {"type": "string", "example": "Login successful"}}}, "AuthToken": {"type": "object", "required": ["accessToken", "refreshToken", "expiresAt", "tokenType"], "properties": {"accessToken": {"type": "string", "description": "JWT access token"}, "refreshToken": {"type": "string", "description": "JWT refresh token"}, "expiresAt": {"type": "string", "format": "date-time", "description": "Token expiration timestamp"}, "tokenType": {"type": "string", "enum": ["Bearer"], "default": "Bearer"}}}, "User": {"type": "object", "required": ["id", "name", "email", "phone", "role", "assignedProperties", "isActive", "createdAt", "lastLogin", "permissions"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique user identifier"}, "name": {"type": "string", "description": "Full name of the user"}, "email": {"type": "string", "format": "email", "description": "Email address"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$", "description": "Phone number in E.164 format"}, "role": {"$ref": "#/components/schemas/UserRole"}, "assignedProperties": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "List of property IDs assigned to user"}, "isActive": {"type": "boolean", "description": "Whether the user account is active"}, "createdAt": {"type": "string", "format": "date-time", "description": "Account creation timestamp"}, "lastLogin": {"type": "string", "format": "date-time", "description": "Last login timestamp"}, "permissions": {"$ref": "#/components/schemas/UserPermissions"}, "avatar": {"type": "string", "format": "uri", "description": "URL to user's avatar image"}, "timezone": {"type": "string", "description": "User's timezone (IANA format)"}, "language": {"type": "string", "enum": ["en", "hi", "te"], "default": "en", "description": "Preferred language"}}}, "UserRole": {"type": "string", "enum": ["super_admin", "property_manager", "office_manager", "security_personnel", "maintenance_staff", "construction_supervisor"], "description": "User role determining access permissions"}, "UserPermissions": {"type": "object", "required": ["canViewDashboard", "canManageProperties", "canManageOffice", "canManageSecurity", "canManageMaintenance", "canManageUsers", "canViewReports", "canExportData", "allowedScreens", "allowedActions"], "properties": {"canViewDashboard": {"type": "boolean"}, "canManageProperties": {"type": "boolean"}, "canManageOffice": {"type": "boolean"}, "canManageSecurity": {"type": "boolean"}, "canManageMaintenance": {"type": "boolean"}, "canManageUsers": {"type": "boolean"}, "canViewReports": {"type": "boolean"}, "canExportData": {"type": "boolean"}, "allowedScreens": {"type": "array", "items": {"type": "string"}, "description": "List of screens user can access"}, "allowedActions": {"type": "array", "items": {"type": "string", "enum": ["create", "read", "update", "delete", "export", "import"]}, "description": "List of actions user can perform"}}}}}}