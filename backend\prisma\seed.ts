import { PrismaClient, UserRole, PropertyType, SystemType, OfficeType } from '@prisma/client';
import { AuthService } from '../src/lib/auth';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create default admin user
  const adminEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123';
  const adminName = process.env.DEFAULT_ADMIN_NAME || 'System Administrator';

  const hashedPassword = await AuthService.hashPassword(adminPassword);

  const admin = await prisma.user.upsert({
    where: { email: adminEmail },
    update: {},
    create: {
      name: adminName,
      email: adminEmail,
      phone: '+919999999999',
      password: hashedPassword,
      role: UserRole.SUPER_ADMIN,
      isActive: true,
      timezone: 'Asia/Kolkata',
      language: 'en',
    },
  });

  console.log('✅ Created admin user:', admin.email);

  // Create sample properties
  const properties = [
    {
      name: 'Jubilee Hills Residence',
      type: PropertyType.RESIDENTIAL,
      address: 'Road No. 36, Jubilee Hills, Hyderabad, Telangana 500033',
      description: 'Luxury residential property in prime Jubilee Hills location',
      latitude: 17.4239,
      longitude: 78.4738,
      images: [],
    },
    {
      name: 'Banjara Hills Office Complex',
      type: PropertyType.OFFICE,
      address: 'Road No. 12, Banjara Hills, Hyderabad, Telangana 500034',
      description: 'Modern office complex with state-of-the-art facilities',
      latitude: 17.4126,
      longitude: 78.4482,
      images: [],
    },
    {
      name: 'Gandipet Construction Site',
      type: PropertyType.CONSTRUCTION,
      address: 'Gandipet, Hyderabad, Telangana 500075',
      description: 'Ongoing residential construction project',
      latitude: 17.3616,
      longitude: 78.2747,
      images: [],
    },
  ];

  const createdProperties = [];
  for (const propertyData of properties) {
    const property = await prisma.property.upsert({
      where: { name: propertyData.name },
      update: {},
      create: propertyData,
    });

    // Create system statuses for each property
    const systemTypes: SystemType[] = ['WATER', 'ELECTRICITY', 'SECURITY', 'INTERNET', 'OTT', 'MAINTENANCE'];
    
    for (const systemType of systemTypes) {
      await prisma.systemStatus.upsert({
        where: {
          propertyId_systemType: {
            propertyId: property.id,
            systemType,
          },
        },
        update: {},
        create: {
          propertyId: property.id,
          systemType,
          status: 'OPERATIONAL',
          description: `${systemType.toLowerCase()} system operational`,
          healthScore: Math.floor(Math.random() * 20) + 80, // 80-100
        },
      });
    }

    createdProperties.push(property);
    console.log('✅ Created property:', property.name);
  }

  // Create sample users with different roles
  const users = [
    {
      name: 'Property Manager',
      email: '<EMAIL>',
      phone: '+919999999998',
      role: UserRole.PROPERTY_MANAGER,
      assignedProperties: [createdProperties[0].id, createdProperties[1].id],
    },
    {
      name: 'Office Manager',
      email: '<EMAIL>',
      phone: '+919999999997',
      role: UserRole.OFFICE_MANAGER,
      assignedProperties: [],
    },
    {
      name: 'Security Personnel',
      email: '<EMAIL>',
      phone: '+919999999996',
      role: UserRole.SECURITY_PERSONNEL,
      assignedProperties: [createdProperties[0].id],
    },
    {
      name: 'Maintenance Staff',
      email: '<EMAIL>',
      phone: '+919999999995',
      role: UserRole.MAINTENANCE_STAFF,
      assignedProperties: [createdProperties[0].id, createdProperties[1].id],
    },
    {
      name: 'Construction Supervisor',
      email: '<EMAIL>',
      phone: '+919999999994',
      role: UserRole.CONSTRUCTION_SUPERVISOR,
      assignedProperties: [createdProperties[2].id],
    },
  ];

  for (const userData of users) {
    const hashedUserPassword = await AuthService.hashPassword('password123');
    
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        name: userData.name,
        email: userData.email,
        phone: userData.phone,
        password: hashedUserPassword,
        role: userData.role,
        isActive: true,
        timezone: 'Asia/Kolkata',
        language: 'en',
      },
    });

    // Assign properties to user
    for (const propertyId of userData.assignedProperties) {
      await prisma.userProperty.upsert({
        where: {
          userId_propertyId: {
            userId: user.id,
            propertyId,
          },
        },
        update: {},
        create: {
          userId: user.id,
          propertyId,
        },
      });
    }

    console.log('✅ Created user:', user.email);
  }

  // Create sample offices
  const offices = [
    {
      name: 'Main Office - Hyderabad',
      type: OfficeType.OFFICE,
      address: 'HITEC City, Hyderabad, Telangana 500081',
      latitude: 17.4435,
      longitude: 78.3772,
      workingHours: {
        monday: { start: '09:00', end: '18:00' },
        tuesday: { start: '09:00', end: '18:00' },
        wednesday: { start: '09:00', end: '18:00' },
        thursday: { start: '09:00', end: '18:00' },
        friday: { start: '09:00', end: '18:00' },
        saturday: { start: '09:00', end: '14:00' },
        sunday: { closed: true },
      },
    },
    {
      name: 'Gandipet Construction Site',
      type: OfficeType.CONSTRUCTION_SITE,
      address: 'Gandipet, Hyderabad, Telangana 500075',
      latitude: 17.3616,
      longitude: 78.2747,
      workingHours: {
        monday: { start: '08:00', end: '17:00' },
        tuesday: { start: '08:00', end: '17:00' },
        wednesday: { start: '08:00', end: '17:00' },
        thursday: { start: '08:00', end: '17:00' },
        friday: { start: '08:00', end: '17:00' },
        saturday: { start: '08:00', end: '17:00' },
        sunday: { closed: true },
      },
    },
  ];

  const createdOffices = [];
  for (const officeData of offices) {
    const office = await prisma.office.upsert({
      where: { name: officeData.name },
      update: {},
      create: officeData,
    });

    createdOffices.push(office);
    console.log('✅ Created office:', office.name);
  }

  // Create sample employees
  const employees = [
    {
      officeId: createdOffices[0].id,
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+919999999993',
      employeeId: 'EMP001',
      designation: 'Software Engineer',
      department: 'IT',
      joinDate: new Date('2023-01-15'),
    },
    {
      officeId: createdOffices[0].id,
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '+919999999992',
      employeeId: 'EMP002',
      designation: 'Project Manager',
      department: 'Operations',
      joinDate: new Date('2023-02-01'),
    },
    {
      officeId: createdOffices[1].id,
      name: 'Mike Johnson',
      email: '<EMAIL>',
      phone: '+919999999991',
      employeeId: 'EMP003',
      designation: 'Site Engineer',
      department: 'Construction',
      joinDate: new Date('2023-03-01'),
    },
  ];

  for (const employeeData of employees) {
    const employee = await prisma.employee.upsert({
      where: { employeeId: employeeData.employeeId },
      update: {},
      create: employeeData,
    });

    console.log('✅ Created employee:', employee.name);
  }

  // Create sample alerts
  const alerts = [
    {
      propertyId: createdProperties[0].id,
      title: 'Water Tank Level Low',
      message: 'Water tank level is below 20%. Immediate attention required.',
      severity: 'HIGH' as const,
      category: 'WATER',
      metadata: {
        tankLevel: 18,
        threshold: 20,
      },
    },
    {
      propertyId: createdProperties[1].id,
      title: 'Security Camera Offline',
      message: 'Camera #3 in parking area is not responding.',
      severity: 'MEDIUM' as const,
      category: 'SECURITY',
      metadata: {
        cameraId: 'CAM003',
        location: 'Parking Area',
      },
    },
  ];

  for (const alertData of alerts) {
    const alert = await prisma.alert.create({
      data: alertData,
    });

    console.log('✅ Created alert:', alert.title);
  }

  // Create sample activities
  await prisma.activity.create({
    data: {
      userId: admin.id,
      propertyId: createdProperties[0].id,
      action: 'SYSTEM_INITIALIZATION',
      description: 'System initialized with sample data',
      metadata: {
        seedVersion: '1.0.0',
        timestamp: new Date().toISOString(),
      },
    },
  });

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Summary:');
  console.log(`- Admin user: ${adminEmail} (password: ${adminPassword})`);
  console.log(`- Properties: ${createdProperties.length}`);
  console.log(`- Users: ${users.length + 1}`);
  console.log(`- Offices: ${createdOffices.length}`);
  console.log(`- Employees: ${employees.length}`);
  console.log(`- Alerts: ${alerts.length}`);
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
