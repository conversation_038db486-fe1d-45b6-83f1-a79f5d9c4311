{"paths": {"/properties": {"get": {"tags": ["Properties"], "summary": "List properties", "description": "Get paginated list of properties with filtering and RBAC", "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"name": "type", "in": "query", "description": "Filter by property type", "schema": {"$ref": "#/components/schemas/PropertyType"}}, {"name": "status", "in": "query", "description": "Filter by property status", "schema": {"$ref": "#/components/schemas/SystemStatus"}}, {"name": "search", "in": "query", "description": "Search by property name or address", "schema": {"type": "string"}}], "responses": {"200": {"description": "Properties retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedPropertiesResponse"}}}}}}, "post": {"tags": ["Properties"], "summary": "Create new property", "description": "Create a new property (Admin/Property Manager only)", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePropertyRequest"}}}}, "responses": {"201": {"description": "Property created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Property"}}}}}}}, "/properties/{propertyId}": {"get": {"tags": ["Properties"], "summary": "Get property details", "description": "Retrieve detailed property information including system statuses", "parameters": [{"$ref": "#/components/parameters/PropertyIdParam"}], "responses": {"200": {"description": "Property retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PropertyDetail"}}}}}}}, "/properties/{propertyId}/systems": {"get": {"tags": ["Properties"], "summary": "Get property system statuses", "description": "Retrieve all system statuses for a property", "parameters": [{"$ref": "#/components/parameters/PropertyIdParam"}, {"name": "systemType", "in": "query", "description": "Filter by system type", "schema": {"$ref": "#/components/schemas/SystemType"}}], "responses": {"200": {"description": "System statuses retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SystemStatus"}}}}}}}}, "/properties/{propertyId}/systems/{systemType}": {"get": {"tags": ["Properties"], "summary": "Get specific system details", "description": "Retrieve detailed information for a specific system type", "parameters": [{"$ref": "#/components/parameters/PropertyIdParam"}, {"name": "systemType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/SystemType"}}], "responses": {"200": {"description": "System details retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemDetail"}}}}}}, "put": {"tags": ["Properties"], "summary": "Update system status", "description": "Update system status and metadata", "parameters": [{"$ref": "#/components/parameters/PropertyIdParam"}, {"name": "systemType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/SystemType"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSystemRequest"}}}}, "responses": {"200": {"description": "System updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemDetail"}}}}}}}, "/dashboard/overview": {"get": {"tags": ["Dashboard"], "summary": "Get dashboard overview", "description": "Retrieve comprehensive dashboard data with system health and statistics", "parameters": [{"name": "propertyIds", "in": "query", "description": "Filter by specific properties (comma-separated)", "schema": {"type": "string"}}, {"name": "timeRange", "in": "query", "description": "Time range for statistics", "schema": {"type": "string", "enum": ["24h", "7d", "30d", "90d"], "default": "24h"}}], "responses": {"200": {"description": "Dashboard overview retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DashboardOverview"}}}}}}}, "/dashboard/alerts": {"get": {"tags": ["Dashboard"], "summary": "Get critical alerts", "description": "Retrieve critical alerts and notifications", "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/LimitParam"}, {"name": "severity", "in": "query", "description": "Filter by alert severity", "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "status", "in": "query", "description": "Filter by alert status", "schema": {"type": "string", "enum": ["open", "acknowledged", "resolved"]}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedAlertsResponse"}}}}}}}, "/offices": {"get": {"tags": ["Office Management"], "summary": "List offices", "description": "Get list of office locations and construction sites", "parameters": [{"name": "type", "in": "query", "description": "Filter by office type", "schema": {"type": "string", "enum": ["office", "construction_site"]}}], "responses": {"200": {"description": "Offices retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Office"}}}}}}}}, "/offices/{officeId}/attendance": {"get": {"tags": ["Office Management"], "summary": "Get attendance records", "description": "Retrieve attendance records for an office", "parameters": [{"name": "officeId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "date", "in": "query", "description": "Specific date (YYYY-MM-DD)", "schema": {"type": "string", "format": "date"}}, {"name": "startDate", "in": "query", "description": "Start date for range query", "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "End date for range query", "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "Attendance records retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttendanceResponse"}}}}}}, "post": {"tags": ["Office Management"], "summary": "Submit attendance", "description": "Submit attendance records for employees/workers", "parameters": [{"name": "officeId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitAttendanceRequest"}}}}, "responses": {"201": {"description": "Attendance submitted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttendanceSubmissionResponse"}}}}}}}}}