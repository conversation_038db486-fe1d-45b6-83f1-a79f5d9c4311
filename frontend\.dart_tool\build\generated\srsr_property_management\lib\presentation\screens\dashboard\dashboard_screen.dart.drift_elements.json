{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:go_router/go_router.dart", "transitive": false}, {"uri": "package:fl_chart/fl_chart.dart", "transitive": false}, {"uri": "package:srsr_property_management/core/constants/app_constants.dart", "transitive": false}, {"uri": "package:srsr_property_management/core/theme/app_theme.dart", "transitive": false}, {"uri": "package:srsr_property_management/presentation/routes/app_router.dart", "transitive": false}, {"uri": "package:srsr_property_management/presentation/screens/main/main_navigation_screen.dart", "transitive": false}], "elements": []}