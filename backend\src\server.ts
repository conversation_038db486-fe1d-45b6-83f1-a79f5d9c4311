import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import swaggerUi from 'swagger-ui-express';
import swaggerJsdoc from 'swagger-jsdoc';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Import middleware
import { errorHandler, notFoundHandler } from '@/middleware/errorHandler';
import { endpointSpecificRateLimit } from '@/middleware/rateLimiter';

// Import routes
import authRoutes from '@/routes/auth';
import propertiesRoutes from '@/routes/properties';
import dashboardRoutes from '@/routes/dashboard';
import officesRoutes from '@/routes/offices';
import usersRoutes from '@/routes/users';
import reportsRoutes from '@/routes/reports';
import notificationsRoutes from '@/routes/notifications';
import filesRoutes from '@/routes/files';

// Import services
import { prisma } from '@/lib/prisma';

const app = express();
const PORT = process.env.PORT || 3001;
const API_VERSION = process.env.API_VERSION || 'v1';

// Create HTTP server for Socket.IO
const server = createServer(app);

// Configure Socket.IO
const io = new SocketIOServer(server, {
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST'],
    credentials: true,
  },
});

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.3',
    info: {
      title: 'SRSR Property Management API',
      version: '1.0.0',
      description: 'Comprehensive API for SRSR Property Management System with RBAC, real-time updates, and multi-property management capabilities.',
      contact: {
        name: 'SRSR Property Management',
        email: '<EMAIL>',
        url: 'https://srsrproperty.com',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: `http://localhost:${PORT}/${API_VERSION}`,
        description: 'Development server',
      },
      {
        url: `https://api.srsrproperty.com/${API_VERSION}`,
        description: 'Production server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token for authentication. Include in Authorization header as \'Bearer {token}\'',
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ['./src/routes/*.ts'], // Path to the API files
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan(process.env.NODE_ENV === 'production' ? 'combined' : 'dev'));
}

// Rate limiting
app.use(endpointSpecificRateLimit);

// Health check endpoint (no auth required)
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
  });
});

// Detailed health check endpoint
app.get('/health/detailed', async (req, res) => {
  try {
    // Test database connection
    const dbStart = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const dbResponseTime = Date.now() - dbStart;

    // Get memory usage
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      services: {
        database: {
          status: 'healthy',
          responseTime: dbResponseTime,
          lastCheck: new Date().toISOString(),
        },
        storage: {
          status: 'healthy',
          responseTime: 0,
          lastCheck: new Date().toISOString(),
        },
      },
      metrics: {
        memoryUsage: Math.round(memoryUsagePercent),
        cpuUsage: 0, // Would need additional monitoring for this
        activeConnections: 0, // Would need connection tracking
      },
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// API Documentation
if (process.env.SWAGGER_ENABLED === 'true') {
  app.use(
    process.env.SWAGGER_PATH || '/api-docs',
    swaggerUi.serve,
    swaggerUi.setup(swaggerSpec, {
      explorer: true,
      customCss: '.swagger-ui .topbar { display: none }',
      customSiteTitle: 'SRSR Property Management API',
    })
  );
}

// API routes
app.use(`/${API_VERSION}/auth`, authRoutes);
app.use(`/${API_VERSION}/properties`, propertiesRoutes);
app.use(`/${API_VERSION}/dashboard`, dashboardRoutes);
app.use(`/${API_VERSION}/offices`, officesRoutes);
app.use(`/${API_VERSION}/users`, usersRoutes);
app.use(`/${API_VERSION}/reports`, reportsRoutes);
app.use(`/${API_VERSION}/notifications`, notificationsRoutes);
app.use(`/${API_VERSION}/files`, filesRoutes);

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  // Join property-specific rooms
  socket.on('join-property', (propertyId: string) => {
    socket.join(`property:${propertyId}`);
    console.log(`Socket ${socket.id} joined property room: ${propertyId}`);
  });

  // Join dashboard room
  socket.on('join-dashboard', () => {
    socket.join('dashboard');
    console.log(`Socket ${socket.id} joined dashboard room`);
  });

  // Join office-specific rooms
  socket.on('join-office', (officeId: string) => {
    socket.join(`office:${officeId}`);
    console.log(`Socket ${socket.id} joined office room: ${officeId}`);
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Export io for use in other modules
export { io };

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  
  server.close(() => {
    console.log('HTTP server closed');
  });
  
  await prisma.$disconnect();
  console.log('Database connection closed');
  
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  
  server.close(() => {
    console.log('HTTP server closed');
  });
  
  await prisma.$disconnect();
  console.log('Database connection closed');
  
  process.exit(0);
});

// Start server
if (process.env.NODE_ENV !== 'test') {
  server.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📚 API Documentation: http://localhost:${PORT}${process.env.SWAGGER_PATH || '/api-docs'}`);
    console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
    console.log(`🔌 WebSocket Server: ws://localhost:${PORT}`);
  });
}

export default app;
