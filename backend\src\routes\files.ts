import { Router } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import { validateRequest, asyncHandler, AppError } from '@/middleware/errorHandler';
import { authenticate, requireAction } from '@/middleware/auth';
import { uploadRateLimit } from '@/middleware/rateLimiter';
import { prisma } from '@/lib/prisma';
import { FileUploadResponse } from '@/types';

const router = Router();

// Apply authentication and rate limiting
router.use(authenticate);
router.use(uploadRateLimit);

// Ensure upload directory exists
const uploadDir = process.env.UPLOAD_PATH || './uploads';
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const subDir = path.join(uploadDir, file.fieldname);
    if (!fs.existsSync(subDir)) {
      fs.mkdirSync(subDir, { recursive: true });
    }
    cb(null, subDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  },
});

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/webp,application/pdf').split(',');
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new AppError(`File type ${file.mimetype} not allowed`, 400, 'INVALID_FILE_TYPE'));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB default
    files: 5, // Max 5 files per request
  },
});

/**
 * @swagger
 * /files/upload/avatar:
 *   post:
 *     tags: [Files]
 *     summary: Upload user avatar
 *     description: Upload and process user avatar image
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               avatar:
 *                 type: string
 *                 format: binary
 *                 description: Avatar image file (JPEG, PNG, WebP)
 *     responses:
 *       200:
 *         description: Avatar uploaded successfully
 *       400:
 *         description: Invalid file or file too large
 */
router.post('/upload/avatar',
  requireAction('update'),
  upload.single('avatar'),
  asyncHandler(async (req, res) => {
    const user = req.user!;
    const file = req.file;

    if (!file) {
      throw new AppError('No file uploaded', 400, 'NO_FILE_UPLOADED');
    }

    // Process image if it's an image file
    let processedFilePath = file.path;
    if (file.mimetype.startsWith('image/')) {
      const processedFileName = `processed-${file.filename}`;
      processedFilePath = path.join(path.dirname(file.path), processedFileName);

      await sharp(file.path)
        .resize(200, 200, {
          fit: 'cover',
          position: 'center',
        })
        .jpeg({ quality: 90 })
        .toFile(processedFilePath);

      // Remove original file
      fs.unlinkSync(file.path);
    }

    // Generate URL
    const fileUrl = `/files/avatar/${path.basename(processedFilePath)}`;

    // Update user avatar
    await prisma.user.update({
      where: { id: user.id },
      data: { avatar: fileUrl },
    });

    // Log activity
    await prisma.activity.create({
      data: {
        userId: user.id,
        action: 'UPDATE_AVATAR',
        description: 'Updated profile avatar',
        metadata: {
          fileName: file.originalname,
          fileSize: file.size,
          fileType: file.mimetype,
        },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
      },
    });

    const response: FileUploadResponse = {
      filename: path.basename(processedFilePath),
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      url: fileUrl,
      uploadedAt: new Date().toISOString(),
    };

    res.json({
      success: true,
      data: response,
      message: 'Avatar uploaded successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /files/upload/property-images:
 *   post:
 *     tags: [Files]
 *     summary: Upload property images
 *     description: Upload multiple images for a property
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Property image files (JPEG, PNG, WebP)
 *               propertyId:
 *                 type: string
 *                 format: uuid
 *                 description: Property ID
 *     responses:
 *       200:
 *         description: Images uploaded successfully
 */
router.post('/upload/property-images',
  requireAction('update'),
  upload.array('images', 5),
  asyncHandler(async (req, res) => {
    const user = req.user!;
    const files = req.files as Express.Multer.File[];
    const { propertyId } = req.body;

    if (!files || files.length === 0) {
      throw new AppError('No files uploaded', 400, 'NO_FILES_UPLOADED');
    }

    if (!propertyId) {
      throw new AppError('Property ID is required', 400, 'PROPERTY_ID_REQUIRED');
    }

    // Check if property exists and user has access
    const property = await prisma.property.findFirst({
      where: {
        id: propertyId,
        ...(user.role !== 'SUPER_ADMIN' && {
          assignedUsers: {
            some: { userId: user.id },
          },
        }),
      },
    });

    if (!property) {
      throw new AppError('Property not found or access denied', 404, 'PROPERTY_NOT_FOUND');
    }

    const uploadedFiles: FileUploadResponse[] = [];

    // Process each image
    for (const file of files) {
      let processedFilePath = file.path;
      
      if (file.mimetype.startsWith('image/')) {
        const processedFileName = `processed-${file.filename}`;
        processedFilePath = path.join(path.dirname(file.path), processedFileName);

        await sharp(file.path)
          .resize(1200, 800, {
            fit: 'inside',
            withoutEnlargement: true,
          })
          .jpeg({ quality: 85 })
          .toFile(processedFilePath);

        // Remove original file
        fs.unlinkSync(file.path);
      }

      const fileUrl = `/files/property-images/${path.basename(processedFilePath)}`;
      
      uploadedFiles.push({
        filename: path.basename(processedFilePath),
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        url: fileUrl,
        uploadedAt: new Date().toISOString(),
      });
    }

    // Update property images
    const imageUrls = uploadedFiles.map(file => file.url);
    await prisma.property.update({
      where: { id: propertyId },
      data: {
        images: {
          push: imageUrls,
        },
      },
    });

    // Log activity
    await prisma.activity.create({
      data: {
        userId: user.id,
        propertyId,
        action: 'UPLOAD_PROPERTY_IMAGES',
        description: `Uploaded ${uploadedFiles.length} images for property`,
        metadata: {
          imageCount: uploadedFiles.length,
          totalSize: uploadedFiles.reduce((sum, file) => sum + file.size, 0),
        },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
      },
    });

    res.json({
      success: true,
      data: uploadedFiles,
      message: `${uploadedFiles.length} images uploaded successfully`,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /files/upload/documents:
 *   post:
 *     tags: [Files]
 *     summary: Upload documents
 *     description: Upload documents (PDF, images)
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               documents:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Document files
 *               category:
 *                 type: string
 *                 description: Document category
 *               propertyId:
 *                 type: string
 *                 format: uuid
 *                 description: Related property ID (optional)
 *     responses:
 *       200:
 *         description: Documents uploaded successfully
 */
router.post('/upload/documents',
  requireAction('create'),
  upload.array('documents', 5),
  asyncHandler(async (req, res) => {
    const user = req.user!;
    const files = req.files as Express.Multer.File[];
    const { category, propertyId } = req.body;

    if (!files || files.length === 0) {
      throw new AppError('No files uploaded', 400, 'NO_FILES_UPLOADED');
    }

    const uploadedFiles: FileUploadResponse[] = [];

    // Process each document
    for (const file of files) {
      const fileUrl = `/files/documents/${file.filename}`;
      
      uploadedFiles.push({
        filename: file.filename,
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        url: fileUrl,
        uploadedAt: new Date().toISOString(),
      });
    }

    // Log activity
    await prisma.activity.create({
      data: {
        userId: user.id,
        propertyId: propertyId || null,
        action: 'UPLOAD_DOCUMENTS',
        description: `Uploaded ${uploadedFiles.length} documents`,
        metadata: {
          category: category || 'general',
          documentCount: uploadedFiles.length,
          totalSize: uploadedFiles.reduce((sum, file) => sum + file.size, 0),
        },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
      },
    });

    res.json({
      success: true,
      data: uploadedFiles,
      message: `${uploadedFiles.length} documents uploaded successfully`,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /files/{category}/{filename}:
 *   get:
 *     tags: [Files]
 *     summary: Serve uploaded file
 *     description: Serve uploaded files
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *           enum: [avatar, property-images, documents]
 *       - in: path
 *         name: filename
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: File served successfully
 *       404:
 *         description: File not found
 */
router.get('/:category/:filename',
  asyncHandler(async (req, res) => {
    const { category, filename } = req.params;
    
    // Validate category
    const allowedCategories = ['avatar', 'property-images', 'documents'];
    if (!allowedCategories.includes(category)) {
      throw new AppError('Invalid file category', 400, 'INVALID_CATEGORY');
    }

    const filePath = path.join(uploadDir, category, filename);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new AppError('File not found', 404, 'FILE_NOT_FOUND');
    }

    // Get file stats
    const stats = fs.statSync(filePath);
    const fileExtension = path.extname(filename).toLowerCase();
    
    // Set appropriate content type
    let contentType = 'application/octet-stream';
    if (['.jpg', '.jpeg'].includes(fileExtension)) contentType = 'image/jpeg';
    else if (fileExtension === '.png') contentType = 'image/png';
    else if (fileExtension === '.webp') contentType = 'image/webp';
    else if (fileExtension === '.pdf') contentType = 'application/pdf';

    // Set headers
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1 year cache

    // Stream file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  })
);

export default router;
