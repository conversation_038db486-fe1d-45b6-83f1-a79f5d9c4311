import { Router } from 'express';
import { UserRole } from '@prisma/client';
import { prisma } from '@/lib/prisma';
import { validateRequest, asyncHandler, AppError } from '@/middleware/errorHandler';
import { authenticate, authorize, requireAction } from '@/middleware/auth';
import { generalRateLimit } from '@/middleware/rateLimiter';
import { paginationSchema } from '@/lib/validation';
import { z } from 'zod';

const router = Router();

// Apply authentication and rate limiting
router.use(authenticate);
router.use(generalRateLimit);

// Notification schemas
const createNotificationSchema = z.object({
  title: z.string().min(5, 'Title must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
  type: z.enum(['INFO', 'WARNING', 'ERROR', 'SUCCESS']),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  targetUsers: z.array(z.string().uuid()).optional(),
  targetRoles: z.array(z.enum(['SUPER_ADMIN', 'PROPERTY_MANAGER', 'OFFICE_MANAGER', 'SECURITY_PERSONNEL', 'MAINTENANCE_STAFF', 'CONSTRUCTION_SUPERVISOR'])).optional(),
  propertyId: z.string().uuid().optional(),
  scheduledFor: z.string().datetime().optional(),
  expiresAt: z.string().datetime().optional(),
});

const notificationQuerySchema = paginationSchema.extend({
  type: z.enum(['INFO', 'WARNING', 'ERROR', 'SUCCESS']).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  isRead: z.boolean().optional(),
  propertyId: z.string().uuid().optional(),
});

/**
 * @swagger
 * /notifications:
 *   get:
 *     tags: [Notifications]
 *     summary: Get user notifications
 *     description: Retrieve notifications for the current user
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [INFO, WARNING, ERROR, SUCCESS]
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, URGENT]
 *       - in: query
 *         name: isRead
 *         schema:
 *           type: boolean
 *     responses:
 *       200:
 *         description: Notifications retrieved successfully
 */
router.get('/',
  validateRequest(notificationQuerySchema, 'query'),
  asyncHandler(async (req, res) => {
    const { page, limit, type, priority, isRead, propertyId } = req.query as any;
    const user = req.user!;

    // Build where clause
    let whereClause: any = {
      OR: [
        { targetUsers: { has: user.id } },
        { targetRoles: { has: user.role } },
        { isGlobal: true },
      ],
      AND: [
        {
          OR: [
            { scheduledFor: null },
            { scheduledFor: { lte: new Date() } },
          ],
        },
        {
          OR: [
            { expiresAt: null },
            { expiresAt: { gte: new Date() } },
          ],
        },
      ],
    };

    // Apply filters
    if (type) whereClause.type = type;
    if (priority) whereClause.priority = priority;
    if (propertyId) {
      // Check if user has access to this property
      if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
        whereClause.propertyId = { in: [] }; // No results
      } else {
        whereClause.propertyId = propertyId;
      }
    }

    // Handle read status filter
    if (isRead !== undefined) {
      if (isRead) {
        whereClause.readBy = { has: user.id };
      } else {
        whereClause.NOT = { readBy: { has: user.id } };
      }
    }

    // Get total count
    const total = await prisma.notification.count({ where: whereClause });

    // Get notifications with pagination
    const notifications = await prisma.notification.findMany({
      where: whereClause,
      include: {
        property: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' },
      ],
      skip: (page - 1) * limit,
      take: limit,
    });

    // Transform notifications to include read status
    const notificationsWithReadStatus = notifications.map(notification => ({
      ...notification,
      isRead: notification.readBy.includes(user.id),
      readBy: undefined, // Remove readBy array from response
    }));

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        data: notificationsWithReadStatus,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /notifications:
 *   post:
 *     tags: [Notifications]
 *     summary: Create notification
 *     description: Create a new notification (Admin only)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateNotificationRequest'
 *     responses:
 *       201:
 *         description: Notification created successfully
 */
router.post('/',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER], 'canManageUsers'),
  requireAction('create'),
  validateRequest(createNotificationSchema),
  asyncHandler(async (req, res) => {
    const notificationData = req.body;
    const user = req.user!;

    // Determine if notification is global
    const isGlobal = !notificationData.targetUsers && !notificationData.targetRoles;

    // Create notification
    const notification = await prisma.notification.create({
      data: {
        title: notificationData.title,
        message: notificationData.message,
        type: notificationData.type,
        priority: notificationData.priority,
        targetUsers: notificationData.targetUsers || [],
        targetRoles: notificationData.targetRoles || [],
        propertyId: notificationData.propertyId,
        isGlobal,
        scheduledFor: notificationData.scheduledFor ? new Date(notificationData.scheduledFor) : null,
        expiresAt: notificationData.expiresAt ? new Date(notificationData.expiresAt) : null,
        createdBy: user.id,
        readBy: [],
      },
    });

    // Log activity
    await prisma.activity.create({
      data: {
        userId: user.id,
        propertyId: notificationData.propertyId,
        action: 'CREATE_NOTIFICATION',
        description: `Created notification: ${notification.title}`,
        metadata: {
          notificationId: notification.id,
          type: notification.type,
          priority: notification.priority,
        },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
      },
    });

    res.status(201).json({
      success: true,
      data: notification,
      message: 'Notification created successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /notifications/{notificationId}/read:
 *   post:
 *     tags: [Notifications]
 *     summary: Mark notification as read
 *     description: Mark a notification as read for the current user
 *     parameters:
 *       - in: path
 *         name: notificationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Notification marked as read
 */
router.post('/:notificationId/read',
  asyncHandler(async (req, res) => {
    const { notificationId } = req.params;
    const user = req.user!;

    // Check if notification exists and user has access
    const notification = await prisma.notification.findFirst({
      where: {
        id: notificationId,
        OR: [
          { targetUsers: { has: user.id } },
          { targetRoles: { has: user.role } },
          { isGlobal: true },
        ],
      },
    });

    if (!notification) {
      throw new AppError('Notification not found', 404, 'NOTIFICATION_NOT_FOUND');
    }

    // Add user to readBy array if not already present
    if (!notification.readBy.includes(user.id)) {
      await prisma.notification.update({
        where: { id: notificationId },
        data: {
          readBy: {
            push: user.id,
          },
        },
      });
    }

    res.json({
      success: true,
      message: 'Notification marked as read',
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /notifications/mark-all-read:
 *   post:
 *     tags: [Notifications]
 *     summary: Mark all notifications as read
 *     description: Mark all accessible notifications as read for the current user
 *     responses:
 *       200:
 *         description: All notifications marked as read
 */
router.post('/mark-all-read',
  asyncHandler(async (req, res) => {
    const user = req.user!;

    // Get all unread notifications for the user
    const unreadNotifications = await prisma.notification.findMany({
      where: {
        OR: [
          { targetUsers: { has: user.id } },
          { targetRoles: { has: user.role } },
          { isGlobal: true },
        ],
        NOT: {
          readBy: { has: user.id },
        },
      },
      select: { id: true },
    });

    // Mark all as read
    const updatePromises = unreadNotifications.map(notification =>
      prisma.notification.update({
        where: { id: notification.id },
        data: {
          readBy: {
            push: user.id,
          },
        },
      })
    );

    await Promise.all(updatePromises);

    res.json({
      success: true,
      message: `${unreadNotifications.length} notifications marked as read`,
      data: {
        markedCount: unreadNotifications.length,
      },
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /notifications/unread-count:
 *   get:
 *     tags: [Notifications]
 *     summary: Get unread notification count
 *     description: Get count of unread notifications for the current user
 *     responses:
 *       200:
 *         description: Unread count retrieved successfully
 */
router.get('/unread-count',
  asyncHandler(async (req, res) => {
    const user = req.user!;

    const unreadCount = await prisma.notification.count({
      where: {
        OR: [
          { targetUsers: { has: user.id } },
          { targetRoles: { has: user.role } },
          { isGlobal: true },
        ],
        NOT: {
          readBy: { has: user.id },
        },
        AND: [
          {
            OR: [
              { scheduledFor: null },
              { scheduledFor: { lte: new Date() } },
            ],
          },
          {
            OR: [
              { expiresAt: null },
              { expiresAt: { gte: new Date() } },
            ],
          },
        ],
      },
    });

    res.json({
      success: true,
      data: {
        unreadCount,
      },
      timestamp: new Date().toISOString(),
    });
  })
);

export default router;
